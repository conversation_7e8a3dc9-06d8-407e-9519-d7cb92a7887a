/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2024/6/26
 */

import 'package:flutter/material.dart';
import 'package:smart_home/device/aggregation/aggregation_card/env_card/model/agg_env_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_camera_card_wrapper.dart';
import 'package:smart_home/device/device_view_model/Dehumidifier_card_view_model.dart';
import 'package:smart_home/device/device_view_model/acousto_light_card_view_model.dart';
import 'package:smart_home/device/device_view_model/add_device_card_view_model.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_card_view_model.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_quick_card_view_model.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_quick_kj320_view_model.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_quick_kj460_view_model.dart';
import 'package:smart_home/device/device_view_model/air_cleaner_quick_kj665_view_model.dart';
import 'package:smart_home/device/device_view_model/air_condition_44_card_view_model.dart';
import 'package:smart_home/device/device_view_model/air_condition_CJC4_view_model.dart';
import 'package:smart_home/device/device_view_model/air_condition_card_KFR_50L_view_model.dart';
import 'package:smart_home/device/device_view_model/air_condition_card_view_model.dart';
import 'package:smart_home/device/device_view_model/air_pipe_line_view_model.dart';
import 'package:smart_home/device/device_view_model/air_smart_control_card_view_model.dart';
import 'package:smart_home/device/device_view_model/aromatherapy_machine_card_view_model.dart';
import 'package:smart_home/device/device_view_model/baby_cook_card_view_model.dart';
import 'package:smart_home/device/device_view_model/background_music_card_view_model.dart';
import 'package:smart_home/device/device_view_model/bake_oven_new_view_model.dart';
import 'package:smart_home/device/device_view_model/broken_wall_card_view_model.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/cband_motion_sensor_card_view_model.dart';
import 'package:smart_home/device/device_view_model/center_machine_card_view_model.dart';
import 'package:smart_home/device/device_view_model/center_soft_water_card_view_model.dart';
import 'package:smart_home/device/device_view_model/closees_tool_card_view_model.dart';
import 'package:smart_home/device/device_view_model/cloud_clothes_card_view_model.dart';
import 'package:smart_home/device/device_view_model/coffee_machine_card_view_model.dart';
import 'package:smart_home/device/device_view_model/coffee_machine_view_model.dart';
import 'package:smart_home/device/device_view_model/com_air_condition_card_view_model.dart';
import 'package:smart_home/device/device_view_model/control_panel_12_view_model.dart';
import 'package:smart_home/device/device_view_model/control_panel_android4_view_model.dart';
import 'package:smart_home/device/device_view_model/control_panel_linux4_view_model.dart';
import 'package:smart_home/device/device_view_model/device_alarm_cancel_key_view_model.dart';
import 'package:smart_home/device/device_view_model/device_support_alarm_false_view_model.dart';
import 'package:smart_home/device/device_view_model/device_support_power_switch_view_model.dart';
import 'package:smart_home/device/device_view_model/dish_washer_a_card_view_model.dart';
import 'package:smart_home/device/device_view_model/dish_washer_more_view_model.dart';
import 'package:smart_home/device/device_view_model/dish_washer_view_model.dart';
import 'package:smart_home/device/device_view_model/double_drawer_dish_washer_card_view_model.dart';
import 'package:smart_home/device/device_view_model/e_water_heater_card_view_model.dart';
import 'package:smart_home/device/device_view_model/environmental_detector_view_model.dart';
import 'package:smart_home/device/device_view_model/fan_temperature_485_panel_view_model.dart';
import 'package:smart_home/device/device_view_model/fan_temperature_control_panel_view_model.dart';
import 'package:smart_home/device/device_view_model/folding_machine_card_view_model.dart';
import 'package:smart_home/device/device_view_model/food_clean_view_model.dart';
import 'package:smart_home/device/device_view_model/freezer_card_view_model.dart';
import 'package:smart_home/device/device_view_model/fridge_card_view_model.dart';
import 'package:smart_home/device/device_view_model/g_water_heater_card_view_model.dart';
import 'package:smart_home/device/device_view_model/garbage_disposal_card_view_model.dart';
import 'package:smart_home/device/device_view_model/garbage_disposal_view_model.dart';
import 'package:smart_home/device/device_view_model/gate_magnetism_view_model.dart';
import 'package:smart_home/device/device_view_model/heat_pumps_big_card_view_model.dart';
import 'package:smart_home/device/device_view_model/heat_pumps_card_view_model.dart';
import 'package:smart_home/device/device_view_model/heating_stove_card_view_model.dart';
import 'package:smart_home/device/device_view_model/home_central_air_condition_040_view_model.dart';
import 'package:smart_home/device/device_view_model/home_central_air_condition_201c_view_model.dart';
import 'package:smart_home/device/device_view_model/home_central_air_condition_view_model.dart';
import 'package:smart_home/device/device_view_model/home_central_out_door_view_model.dart';
import 'package:smart_home/device/device_view_model/humidification_card_view_model.dart';
import 'package:smart_home/device/device_view_model/ice_bar_card_view_model.dart';
import 'package:smart_home/device/device_view_model/ignition_stove_card_view_model.dart';
import 'package:smart_home/device/device_view_model/integrated_stove_card_view_model.dart';
import 'package:smart_home/device/device_view_model/microwave_card_view_model.dart';
import 'package:smart_home/device/device_view_model/new_fan_00040_view_model.dart';
import 'package:smart_home/device/device_view_model/new_fan_19a40_view_model.dart';
import 'package:smart_home/device/device_view_model/new_fan_48040_view_model.dart';
import 'package:smart_home/device/device_view_model/new_fan_base_view_model.dart';
import 'package:smart_home/device/device_view_model/new_wind_air_condition_card_view_model.dart';
import 'package:smart_home/device/device_view_model/nursing_engine_card_view_model.dart';
import 'package:smart_home/device/device_view_model/oven_card_view_model.dart';
import 'package:smart_home/device/device_view_model/oven_new_view_model.dart';
import 'package:smart_home/device/device_view_model/panel_sensor_card_view_model.dart';
import 'package:smart_home/device/device_view_model/prefilter_card_view_model.dart';
import 'package:smart_home/device/device_view_model/pure_water_clean_card_view_model.dart';
import 'package:smart_home/device/device_view_model/purifier_card_view_model.dart';
import 'package:smart_home/device/device_view_model/range_hood_card_view_model.dart';
import 'package:smart_home/device/device_view_model/report_for_repair_card_view_model.dart';
import 'package:smart_home/device/device_view_model/rice_cooker_card_view_model.dart';
import 'package:smart_home/device/device_view_model/safety_440_card_view_model.dart';
import 'package:smart_home/device/device_view_model/safety_alarm_false_view_model.dart';
import 'package:smart_home/device/device_view_model/safety_all_card_view_model.dart';
import 'package:smart_home/device/device_view_model/safety_card_view_model.dart';
import 'package:smart_home/device/device_view_model/safety_dcf_card_view_model.dart';
import 'package:smart_home/device/device_view_model/shoe_box_card_view_model.dart';
import 'package:smart_home/device/device_view_model/single_washer_fx_view_model.dart';
import 'package:smart_home/device/device_view_model/single_washer_view_model.dart';
import 'package:smart_home/device/device_view_model/smart_door_card_view_model.dart';
import 'package:smart_home/device/device_view_model/smart_light_card_view_model.dart';
import 'package:smart_home/device/device_view_model/smart_watch_card_view_model.dart';
import 'package:smart_home/device/device_view_model/socket_1a_card_view_model.dart';
import 'package:smart_home/device/device_view_model/socket_card_view_model.dart';
import 'package:smart_home/device/device_view_model/solar_energy_card_view_model.dart';
import 'package:smart_home/device/device_view_model/song_box_card_view_model.dart';
import 'package:smart_home/device/device_view_model/sound_light_sync_box_card_view_model.dart';
import 'package:smart_home/device/device_view_model/steamer_card_view_model.dart';
import 'package:smart_home/device/device_view_model/sterilizer_card_view_model.dart';
import 'package:smart_home/device/device_view_model/sweeping_robot_card_view_model.dart';
import 'package:smart_home/device/device_view_model/switch_four_key_view_model.dart';
import 'package:smart_home/device/device_view_model/switch_one_key_view_model.dart';
import 'package:smart_home/device/device_view_model/switch_three_key_view_model.dart';
import 'package:smart_home/device/device_view_model/switch_two_key_view_model.dart';
import 'package:smart_home/device/device_view_model/towel_rail_card_view_model.dart';
import 'package:smart_home/device/device_view_model/under_floor_heating_panel.dart';
import 'package:smart_home/device/device_view_model/voice_control_card_view_mode.dart';
import 'package:smart_home/device/device_view_model/wash_device_31_view_model.dart';
import 'package:smart_home/device/device_view_model/wash_shoe_card_view_model.dart';
import 'package:smart_home/device/device_view_model/washer_dryer_view_model.dart';
import 'package:smart_home/device/device_view_model/water_cooling_tower_fan_card_view_model.dart';
import 'package:smart_home/device/device_view_model/wine_cabinet_card_view_model.dart';
import 'package:smart_home/device/device_widget/add_device_card.dart';
import 'package:smart_home/device/device_widget/device_card_small.dart';
import 'package:smart_home/device/device_widget/report_for_repair_card.dart';
import 'package:smart_home/smart_home/widget/unlogin/new_user_pack.dart';

import '../aggregation/agg_nonnet/agg_non_net_view_model.dart';
import '../aggregation/agg_offline/agg_offline_view_model.dart';
import '../aggregation/agg_store/aggregation_device_reducer_support.dart';
import '../aggregation/aggregation_card/curtain_card/model/aggregation_curtain_view_model.dart';
import '../aggregation/aggregation_card/light_card/model/aggregation_light_view_model.dart';
import '../aggregation/aggregation_card/util/aggregation_device_util.dart';
import '../device_info_model/smart_home_device.dart';
import '../device_view_model/card_base_view_model.dart';
import '../device_view_model/coffee_machine_model_card_view_model.dart';
import '../device_view_model/curtain_card_view_model.dart';
import '../device_view_model/device_card_view_model.dart';
import '../device_view_model/double_drawer_fx_washer_card_view_model.dart';
import '../device_view_model/heating_stove_big_card_view_model.dart';
import '../device_view_model/new_fan_48640_view_model.dart';
import '../device_view_model/new_fan_9dc40_view_model.dart';
import '../device_view_model/tv_card_view_model.dart';
import '../device_view_model/wash_device_view_model.dart';
import '../device_widget/camera.dart';
import '../device_widget/device_card_large.dart';
import '../device_widget/device_card_middle.dart';
import '../resize_device_card/resize_base_model.dart';
import 'air_condition_support_device.dart';

enum WidgetType {
  largeDeviceCard,
  smallDeviceCard,
}

typedef DeviceCardViewModelBuilder = DeviceCardViewModel Function(
    SmartHomeDevice smartHomeDevice);

class DeviceCardFactory {
  static final Map<String, DeviceCardViewModelBuilder>
      _cardViewModelRelationMap = <String, DeviceCardViewModelBuilder>{
    '1': (SmartHomeDevice smartHomeDevice) =>
        FridgeCardViewModel(device: smartHomeDevice),
    '2': (SmartHomeDevice smartHomeDevice) =>
        AirConditionCardViewModel(device: smartHomeDevice),
    '4': (SmartHomeDevice smartHomeDevice) =>
        WashDeviceViewModel(device: smartHomeDevice),
    '5': (SmartHomeDevice smartHomeDevice) =>
        WashDeviceViewModel(device: smartHomeDevice),
    '5#5': (SmartHomeDevice smartHomeDevice) =>
        WasherDryerViewModel(device: smartHomeDevice),
    '5#4': (SmartHomeDevice smartHomeDevice) =>
        WasherDryerViewModel(device: smartHomeDevice),
    '31': (SmartHomeDevice smartHomeDevice) =>
        WashDevice31ViewModel(device: smartHomeDevice),
    'KFR-50L/NAA22AU1室内机总成': (SmartHomeDevice smartHomeDevice) =>
        AirConditionKFR50LCardViewModel(device: smartHomeDevice),
    'KFR-50LW/26NAA22AU1套机': (SmartHomeDevice smartHomeDevice) =>
        AirConditionKFR50LCardViewModel(device: smartHomeDevice),
    'KFR-72L/NAA22AU1室内机总成': (SmartHomeDevice smartHomeDevice) =>
        AirConditionKFR50LCardViewModel(device: smartHomeDevice),
    'KFR-72LW/26NAA22AU1套机': (SmartHomeDevice smartHomeDevice) =>
        AirConditionKFR50LCardViewModel(device: smartHomeDevice),
    '3': (SmartHomeDevice smartHomeDevice) =>
        AirConditionCardViewModel(device: smartHomeDevice),
    'd#21': (SmartHomeDevice smartHomeDevice) =>
        HomeCentralAirConditionViewModel(device: smartHomeDevice),
    'd#12': (SmartHomeDevice smartHomeDevice) =>
        HomeCentralAirConditionViewModel(device: smartHomeDevice),
    '20086108008203240d2101504046254557000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            HomeCentralOutdoorAirConditionViewModel(device: smartHomeDevice),
    '20086108008203240d21c64322df88000000c0a857e4843e39056504ebfc8c40':
        (SmartHomeDevice smartHomeDevice) =>
            HomeCentralOutdoorAirConditionViewModel(device: smartHomeDevice),
    '20086108008203240d2100118016060000000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            HomeCentralAirCondition040ViewModel(device: smartHomeDevice),
    '201c10c7088081000d211d3504fb25000000bacd0a3803e6eb6328739d246740':
        (SmartHomeDevice smartHomeDevice) =>
            HomeCentralAirCondition201cViewModel(device: smartHomeDevice),
    '201c10c7088081000d1205464544850000009cd68e692c104e2a333eab95d140':
        (SmartHomeDevice smartHomeDevice) =>
            HomeCentralAirCondition201cViewModel(device: smartHomeDevice),
    '201c10c7088081000d12e9c1f6578a0000008cb3fd74ad33c2823c63ddb1e540':
        (SmartHomeDevice smartHomeDevice) =>
        HomeCentralAirCondition201cViewModel(device: smartHomeDevice),
    '201c1200240008100d12f71c4094f300000080e384181627a2904f4940b24240':
        (SmartHomeDevice smartHomeDevice) =>
        HomeCentralAirCondition201cViewModel(device: smartHomeDevice),
    '6': (SmartHomeDevice smartHomeDevice) =>
        EWaterHeaterCardViewModel(device: smartHomeDevice),
    '20': (SmartHomeDevice smartHomeDevice) =>
        HeatPumpsCardViewModel(device: smartHomeDevice),
    '20#1': (SmartHomeDevice smartHomeDevice) =>
        HeatPumpsBigCardViewModel(device: smartHomeDevice),
    '20#2': (SmartHomeDevice smartHomeDevice) =>
        HeatPumpsBigCardViewModel(device: smartHomeDevice),
    '20#3': (SmartHomeDevice smartHomeDevice) =>
        HeatPumpsBigCardViewModel(device: smartHomeDevice),
    '20#4': (SmartHomeDevice smartHomeDevice) =>
        HeatPumpsBigCardViewModel(device: smartHomeDevice),
    '20#5': (SmartHomeDevice smartHomeDevice) =>
        HeatPumpsBigCardViewModel(device: smartHomeDevice),
    'a': (SmartHomeDevice smartHomeDevice) =>
        DishWasherACardViewModel(device: smartHomeDevice),
    '18': (SmartHomeDevice smartHomeDevice) =>
        GWaterHeaterCardViewModel(device: smartHomeDevice),
    '3a': (SmartHomeDevice smartHomeDevice) =>
        IceBarCardViewModel(device: smartHomeDevice),
    '16': (SmartHomeDevice smartHomeDevice) =>
        FreezerCardViewModel(device: smartHomeDevice),
    '19': (SmartHomeDevice smartHomeDevice) =>
        HeatingStoveBigCardViewModel(device: smartHomeDevice),
    '25': (SmartHomeDevice smartHomeDevice) =>
        HeatingStoveCardViewModel(device: smartHomeDevice),
    'b': (SmartHomeDevice smartHomeDevice) =>
        SterilizerCardViewModel(device: smartHomeDevice),
    '9': (SmartHomeDevice smartHomeDevice) =>
        RangeHoodCardViewModel(device: smartHomeDevice),
    '1d': (SmartHomeDevice smartHomeDevice) =>
        IgnitionStoveCardViewModel(device: smartHomeDevice),
    '1e': (SmartHomeDevice smartHomeDevice) =>
        OvenCardViewModel(device: smartHomeDevice),
    '3e': (SmartHomeDevice smartHomeDevice) =>
        OvenCardViewModel(device: smartHomeDevice),
    '1a': (SmartHomeDevice smartHomeDevice) =>
        SteamerCardViewModel(device: smartHomeDevice),
    '10': (SmartHomeDevice smartHomeDevice) =>
        SmartLightCardViewModel(device: smartHomeDevice),
    '10#11': (SmartHomeDevice smartHomeDevice) =>
        SmartLightCardViewModel(device: smartHomeDevice),
    '10#20': (SmartHomeDevice smartHomeDevice) =>
        SmartLightCardViewModel(device: smartHomeDevice),
    '41': (SmartHomeDevice smartHomeDevice) =>
        DeviceSupportPowerSwitchViewModel(device: smartHomeDevice),
    '7': (SmartHomeDevice smartHomeDevice) =>
        MicrowaveCardViewModel(device: smartHomeDevice),
    '8': (SmartHomeDevice smartHomeDevice) =>
        WineCabinetCardViewModel(device: smartHomeDevice),
    '44': (SmartHomeDevice smartHomeDevice) =>
        AirCondition44CardViewModel(device: smartHomeDevice),
    //------------------- 空气净化器 start ----------------------------------------
    '21#1': (SmartHomeDevice smartHomeDevice) =>
        AirCleanerCardViewModel(device: smartHomeDevice),
    '2054a00039044b142101381b1278fd000000f677f03f9cbc9e2f29a3d9a07b40':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickCardViewModel(device: smartHomeDevice),
    '2054a00039044b142101c65e30bd64000000b72b1ab6e65b40b82dce7450b140':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickCardViewModel(device: smartHomeDevice),
    '2054a00039044b14210191108f4d8f00000095aaca9f1df6330be02dd7faa840':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickCardViewModel(device: smartHomeDevice),
    '2054a00039044b142101fb89dfd236000000bd739b1849b6722591bfa319c840':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickCardViewModel(device: smartHomeDevice),
    '2054a00039044b14210100000000000000000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickKj460ViewModel(device: smartHomeDevice),
    '2054a00039044b142101da2bb7c1c9000000b17cfe66d7996e7051a262340740':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickCardViewModel(device: smartHomeDevice),
    '2054a00039044b142101c7badc924b0000005036d0b42ca826d8de5c256b6f40':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickCardViewModel(device: smartHomeDevice),
    '2054a00039044b142101b777deda9200000031dd5b2023247419c191d58d5440':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickKj320ViewModel(device: smartHomeDevice),
    '200c5208003101102101c64aa8bb0b000000fc75b1eb79ef465c23e6a705a640':
        (SmartHomeDevice smartHomeDevice) =>
            AirCleanerQuickKj665ViewModel(device: smartHomeDevice),
    //------------------- 空气净化器 end ----------------------------------------
    '21#4': (SmartHomeDevice smartHomeDevice) =>
        HumidificationCardViewModel(device: smartHomeDevice),
    '21#5': (SmartHomeDevice smartHomeDevice) =>
        DehumidifierCardViewModel(device: smartHomeDevice),
    '21#6': (SmartHomeDevice smartHomeDevice) =>
        AromatherapyMachineCardViewModel(device: smartHomeDevice),
    '2a#3': (SmartHomeDevice smartHomeDevice) =>
        FoldingMachineCardViewModel(device: smartHomeDevice),
    '2a#1': (SmartHomeDevice smartHomeDevice) =>
        ShoeBoxCardViewModel(device: smartHomeDevice),
    '2a#4': (SmartHomeDevice smartHomeDevice) =>
        NursingEngineCardViewModel(device: smartHomeDevice),
    '2b#1': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '2b#2': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '2b#3': (SmartHomeDevice smartHomeDevice) =>
        WashShoeCardViewModel(device: smartHomeDevice),
    '14#10': (SmartHomeDevice smartHomeDevice) =>
        CloudClothesCardViewModel(device: smartHomeDevice),
    '14#6': (SmartHomeDevice smartHomeDevice) =>
        SocketCardViewModel(device: smartHomeDevice),
    '14#11': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '14#15': (SmartHomeDevice smartHomeDevice) =>
        AirSmartControlCardViewModel(device: smartHomeDevice),
    '14#1a': (SmartHomeDevice smartHomeDevice) =>
        Socket1aCardViewModel(device: smartHomeDevice),
    '201c80c70c50031c141aeec68fea2800000020175ccab9bc91d08db46121bf40':
        (SmartHomeDevice smartHomeDevice) =>
            SmartDoorCardViewModel(device: smartHomeDevice),
    '14#21': (SmartHomeDevice smartHomeDevice) =>
        DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    '14#25': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '14#28': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '14#22': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '22#3': (SmartHomeDevice smartHomeDevice) =>
        CenterSoftWaterCardViewModel(device: smartHomeDevice),
    '22#4': (SmartHomeDevice smartHomeDevice) =>
        PurifierCardViewModel(device: smartHomeDevice),
    '22#5': (SmartHomeDevice smartHomeDevice) =>
        CenterMachineCardViewModel(device: smartHomeDevice),
    '22#9': (SmartHomeDevice smartHomeDevice) =>
        PrefilterCardViewModel(device: smartHomeDevice),
    '22#a': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '22#f': (SmartHomeDevice smartHomeDevice) =>
        PureWaterCleanCardViewModel(device: smartHomeDevice),
    '22#10': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '36#2': (SmartHomeDevice smartHomeDevice) =>
        CloseesToolCardViewModel(device: smartHomeDevice),
    '3f#1': (SmartHomeDevice smartHomeDevice) =>
        IntegratedStoveCardViewModel(device: smartHomeDevice),
    '3f#2': (SmartHomeDevice smartHomeDevice) =>
        IntegratedStoveCardViewModel(device: smartHomeDevice),
    '3f#3': (SmartHomeDevice smartHomeDevice) =>
        IntegratedStoveCardViewModel(device: smartHomeDevice),
    '3f#4': (SmartHomeDevice smartHomeDevice) =>
        IntegratedStoveCardViewModel(device: smartHomeDevice),
    '3f#5': (SmartHomeDevice smartHomeDevice) =>
        IntegratedStoveCardViewModel(device: smartHomeDevice),
    '3f#6': (SmartHomeDevice smartHomeDevice) =>
        IntegratedStoveCardViewModel(device: smartHomeDevice),
    '3f#7': (SmartHomeDevice smartHomeDevice) =>
        IntegratedStoveCardViewModel(device: smartHomeDevice),
    '28#9': (SmartHomeDevice smartHomeDevice) =>
        BrokenWallCardViewModel(device: smartHomeDevice),
    '28#5': (SmartHomeDevice smartHomeDevice) =>
        RiceCookerCardViewModel(device: smartHomeDevice),
    '28#b': (SmartHomeDevice smartHomeDevice) =>
        BabyCookCardViewModel(device: smartHomeDevice),
    '28#1': (SmartHomeDevice smartHomeDevice) =>
        CoffeeMachineCardViewModel(device: smartHomeDevice),
    '201c10c704004574280139aa9c5ac6000000d0adf4ac2cbcecde9d7812268840':
        (SmartHomeDevice smartHomeDevice) =>
            CoffeeMachineModelCardViewModel(device: smartHomeDevice),
    '28#11': (SmartHomeDevice smartHomeDevice) =>
        DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    '29#1': (SmartHomeDevice smartHomeDevice) =>
        PanelSensorCardViewModel(device: smartHomeDevice),
    '24#2': (SmartHomeDevice smartHomeDevice) =>
        NewFanBaseViewModel(device: smartHomeDevice),
    '2008610800820324240201518004824200000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            NewFan00040ViewModel(device: smartHomeDevice),
    '2008610800820324240297adc1f58000000059ffd9f30d8999ff6cac48748640':
        (SmartHomeDevice smartHomeDevice) =>
            NewFan48640ViewModel(device: smartHomeDevice),
    '20086108008203242402100297e9dd0000006e1aee96cf8785e425b3ebc19a40':
        (SmartHomeDevice smartHomeDevice) =>
            NewFan19a40ViewModel(device: smartHomeDevice),
    '20086108008203242411f663554cc74a117ad583419138a79684f1f5ba89dc40':
        (SmartHomeDevice smartHomeDevice) =>
            NewFan9dc40ViewModel(device: smartHomeDevice),
    '200861080082032424111cc6dcc2e20000005877d1e4bece3eb38894d1f48040':
        (SmartHomeDevice smartHomeDevice) =>
            NewFan48040ViewModel(device: smartHomeDevice),
    '24': (SmartHomeDevice smartHomeDevice) =>
        NewWindAirConditionCardViewModel(device: smartHomeDevice),
    '24#20': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '11': (SmartHomeDevice smartHomeDevice) =>
        SafetyAllCardViewModel(device: smartHomeDevice),
    '201c80c70c50031c110b36096293f000000092d42bc8523a66aaeaf260f02f40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    '30#1': (SmartHomeDevice smartHomeDevice) =>
        DeviceCardViewModel(device: smartHomeDevice),
    '11#e': (SmartHomeDevice smartHomeDevice) =>
        SafetyDcfCardViewModel(device: smartHomeDevice),
    '11#1': (SmartHomeDevice smartHomeDevice) =>
        SafetyDcfCardViewModel(device: smartHomeDevice),
    '11#2': (SmartHomeDevice smartHomeDevice) =>
        SafetyCardViewModel(device: smartHomeDevice),
    // 门锁（不上报状态及电量）
    '201c80c70c50031c11027df0e8d6690000007fbac0c6366a57d53525158ed440':
        (SmartHomeDevice smartHomeDevice) =>
            Safety440CardViewModel(device: smartHomeDevice),
    '11#7': (SmartHomeDevice smartHomeDevice) =>
        DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    '11#8': (SmartHomeDevice smartHomeDevice) =>
        DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    '12': (SmartHomeDevice smartHomeDevice) =>
        DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    'd#81': (SmartHomeDevice smartHomeDevice) =>
        NewFan19a40ViewModel(device: smartHomeDevice),
    'd#2': (SmartHomeDevice smartHomeDevice) =>
        ComAirConditionCardViewModel(device: smartHomeDevice),
    'CJC-4': (SmartHomeDevice smartHomeDevice) =>
        AirConditionCJC4ViewModel(device: smartHomeDevice),
    // 背景音乐，同音箱
    'f#5': (SmartHomeDevice smartHomeDevice) =>
        BackgroundMusicCardViewModel(device: smartHomeDevice),
    // 音箱，按照大中类
    '3b#1': (SmartHomeDevice smartHomeDevice) =>
        BackgroundMusicCardViewModel(device: smartHomeDevice),
    '37#1': (SmartHomeDevice smartHomeDevice) =>
        WaterCoolingTowerFanCardViewModel(device: smartHomeDevice),
    '34#5': (SmartHomeDevice smartHomeDevice) =>
        SocketCardViewModel(device: smartHomeDevice),
    '1f': (SmartHomeDevice smartHomeDevice) =>
        SolarEnergyCardViewModel(device: smartHomeDevice),
    '25#1': (SmartHomeDevice smartHomeDevice) =>
        DeviceSupportPowerSwitchViewModel(device: smartHomeDevice),
    '14#23': (SmartHomeDevice smartHomeDevice) =>
        TowelRailCardViewModel(device: smartHomeDevice),
    '11#10': (SmartHomeDevice smartHomeDevice) =>
        CBandMotionSensorCardViewModel(device: smartHomeDevice),
    '32#2': (SmartHomeDevice smartHomeDevice) =>
        SmartWatchCardViewModel(device: smartHomeDevice),
    '2c#1': (SmartHomeDevice smartHomeDevice) =>
        GarbageDisposalCardViewModel(device: smartHomeDevice),
    'a#3': (SmartHomeDevice smartHomeDevice) =>
        DoubleDrawerDishwasherCardViewModel(device: smartHomeDevice),
    //------------ 斐雪派克双抽洗碗机 --------------------------
    '201c51890c31c3080a0351993700010000000000160000000000000000001140':
        (SmartHomeDevice smartHomeDevice) =>
            DoubleDrawerFxWasherCardViewModel(device: smartHomeDevice),
    '201c51890c31c3080a0351993800010000000000170000000000000000001140':
        (SmartHomeDevice smartHomeDevice) =>
            DoubleDrawerFxWasherCardViewModel(device: smartHomeDevice),
    'd#22': (SmartHomeDevice smartHomeDevice) =>
        VoiceControlCardViewModel(device: smartHomeDevice),
    'a#1': (SmartHomeDevice smartHomeDevice) =>
        SingleWasherViewModel(device: smartHomeDevice),
    'a#2': (SmartHomeDevice smartHomeDevice) =>
        SingleWasherViewModel(device: smartHomeDevice),
    'd#24': (SmartHomeDevice smartHomeDevice) =>
        AirPipeLineViewModel(device: smartHomeDevice),
    //空气盒子
    '101c120024000810140d00118003940000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('50w000', device: smartHomeDevice),
    //电暖桌
    '201c10c7040045744102d7a5c2ca0f0000007e5c1c8dc65c3562c9a490253c40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //电暖气
    '201c10c70400457441018bf3f8a653000000f736d47780873989207009b1f740':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //地暖温控面板
    '201c80c70c50031c2506f82abf35ca000000b66923d25259ae2a00d0ab43d640':
        (SmartHomeDevice smartHomeDevice) =>
            UnderFloorHeatingPanel(device: smartHomeDevice),
    '201c10c70400457437011beb244fe600000056336fea30b73b04e11647be0540':
        (SmartHomeDevice smartHomeDevice) =>
            WaterCoolingTowerFanCardViewModel(device: smartHomeDevice),
    //电饭煲
    '201c10c7040045742805b886654fda000000d2f64ed19a951282e248e5b71040':
        (SmartHomeDevice smartHomeDevice) =>
            RiceCookerCardViewModel(device: smartHomeDevice),
    '201c10c7040045742805eb077abab90000007bb3b0becf761a6f9807c4a84040':
        (SmartHomeDevice smartHomeDevice) =>
            RiceCookerCardViewModel(device: smartHomeDevice),
    //料理机 28#9
    '201c10c704004574280964171d50600000006f48fe014ce79a30c2d493281540':
        (SmartHomeDevice smartHomeDevice) =>
            BrokenWallCardViewModel(device: smartHomeDevice),
    //吸尘器
    '201c10c7040045742701e452d09c9e000000275844f059dcbc7b22d4a6e78240':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201c10c7040045742701dcb43a02410000000fb4cfe262c64831926edd94e140':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201c10c704004574270153e79a6d35000000a71638fa5e8fdfdc816b5d423140':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),

    ///添加camera型号
    '201c80c70c50031c1201c040c2427100000005c20f206d21185e1676a9b52740':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201fe4a45318f000000eee01d5dd9d95501c7902480ac40':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201318facf9710000005cae21a91e9926cad929f83d1140':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201c4db4901f3000000d126b368293353c731ca81f7da40':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201e1868469c60000000916084761a270b0bf4116b93b40':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201d80e39d5380000004e8792c3df572d057e8295df2f40':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201aca84999250000007afa09df2e5189defb37f4633940':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c12018817561dfe000000b734bd508d81c8a4cc99047bf840':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201a3bdfd123b0000007383a02d45b232698cdacef3e840':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),
    '201c80c70c50031c1201a3bdfd123b0000007013e5f5a7c57e3388bbf605f640':
        (SmartHomeDevice smartHomeDevice) =>
            CameraDeviceCardViewModel(smartHomeDevice),

    //净化魔方 空气净化器
    '111c12002400081021010000005a4e4b32303134303931313031000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '111c120024000810210105400002530000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('521000', device: smartHomeDevice),
    '111c120024000810210105400002750000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    // 牙刷
    '203c920238e04d240f02199e9d795c0000009098f4aa29ecdd392f1f621eb2c0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2054a0000426cb243b01f2bf8d1b8d000000751270448f635a3dc3ef6c942f40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '200c520800310110210107c67485d6828b64ef904ebd1c98a40eec70012aa740':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2054a00039044b142101d5d038a86a0000008b58f361e7043b2ab4dd996d9d40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //晾衣架
    '1100710304824314141000000000000000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //洗碗机 HW4_B71U1
    '201c1200240008100a0201218000960000000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //洗碗机 HW9B176U1
    '201c10c7141202100a0201218000960000000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //洗碗机:一个未接入海极网的逻辑引擎
    '201c10c7141202100a0111508061d5ff857dbe03a6db79cbf4246387b5108840':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //消毒柜
    '111c1200240008100b0402440000660000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('50B000', device: smartHomeDevice),
    //消毒柜 ZQD100F-H708U1
    '201c10c7141202100b01783e6d1e1100000036dd96ee23c025c24520e665e940':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //点火灶
    '201c10c7141202101d010dcfe61554bd6482af93f4e1e5d4acb7ccc919d63240':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201c10c7141202101d810243deb309221ae4c13ec810c57c3f0972581ab0ff40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201c10c7141202101d011477e5d7bb28db5077299d1355c8f037bdf28a8f2f40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201c80c70c50031c30016338eff60612c017784250f04d6567c64cfd5f14b540':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('50B000', device: smartHomeDevice),
    '101c120024000810200100418002490000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('520000', device: smartHomeDevice),
    //烤箱
    '201043cd34d384341e01764fc9144be7f5ac8dce0b95128926ae6269d8f30ac0':
        (SmartHomeDevice smartHomeDevice) =>
            BakeOvenNewViewModel(device: smartHomeDevice),
    '201c10c7141202101a0143165152100000000ce03dfb8de12dc8c563e5e49a40':
        (SmartHomeDevice smartHomeDevice) =>
            OvenNewViewModel(device: smartHomeDevice),
    //油烟机
    '2004b1063460ca3c09025d8b6c7eef72d95c7c04c37fe83699e017a182c47bc0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //SmartLock
    '201c80c70c50031c30011e2df845340a147d494938276616dd698549bc9cdc40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //SOLAR_ENERGY_PJF2H3
    '101c1200240008101f0100418002570000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('51f000', device: smartHomeDevice),
    //SOLAR_ENERGY_TK32
    '111c1200240008101f0200418002065300000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('51f000', device: smartHomeDevice),
    //安防-摄像机
    '201c80c70c50031c12015df8edc75d000000c082b004cad0100284f234dc0b40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //安防-门铃
    '201c80c70c50031c120219f132bd1e0000009c47cd41cbdbd6557966f4251640':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //安防-摄像头
    '201c80c70c50031c12012c3d0c59c4000000f5e271511a3206c65671d57a8e40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //安防-门磁
    '201c80c70c50031c1101d45452eead9bd7cd2eff981b4558563c165daea37640':
        (SmartHomeDevice smartHomeDevice) =>
            GateMagnetismViewModel(device: smartHomeDevice),
    //安防设备
    '201c80c70c50031c11040cf5b6d28e000000b0309a04fb7b4a9be49428c30e40':
        (SmartHomeDevice smartHomeDevice) =>
            SafetyAlarmFalseViewModel(device: smartHomeDevice),
    '201c80c70c50031c11030cf5b6d28e000000bf59f47869b1a90fb2708a04d140':
        (SmartHomeDevice smartHomeDevice) =>
            SafetyAlarmFalseViewModel(device: smartHomeDevice),
    '201c80c70c50031c110a0cf5b6d28e000000bad814e740d0f4288bc343e6ee40':
        (SmartHomeDevice smartHomeDevice) =>
            SafetyAlarmFalseViewModel(device: smartHomeDevice),
    '201c80c70c50031c11060cf5b6d28e00000098fd6f51de1e2b6c348de5239a40':
        (SmartHomeDevice smartHomeDevice) =>
            SafetyAlarmFalseViewModel(device: smartHomeDevice),
    '201c80c70c50031c110b0cf5b6d28e000000ac51eced446102d967f5f5590340':
        (SmartHomeDevice smartHomeDevice) =>
            SafetyAlarmFalseViewModel(device: smartHomeDevice),
    //新风温控面板
    '201c80c70c50031c142c16f7d2ad80000000524cd6bc50d0b4fc9aa6acb19a40':
        (SmartHomeDevice smartHomeDevice) =>
            FanTemperatureControlPanelViewModel(device: smartHomeDevice),
    '20086108008203242411f663554cc7dec95c2a6159fb36ec55c4927176d0b540':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //净水器
    '101c120024000810220100000046373035573030303000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('522000', device: smartHomeDevice),
    //食材净化机
    '200c50022440c318280a203698167b0000007e9d315148d49130a15839a96b40':
        (SmartHomeDevice smartHomeDevice) =>
            FoodCleanKeyViewModel(device: smartHomeDevice),
    //马桶
    '2054a0cc213408243602023fb7dbdf000000db80e9e31b0e2d3f631ee3fc7140':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2054a00039044b142104d5d038a86a00000066014e7f88b0be75ec5e7b166640':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2054a00039044b1421043e20e964930000007db66b2feb0d326f1bab46c2b040':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2054a00039044b142104254e106cca0000002b7239736277c327fa90d1332440':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2018804f20b2cb20101160369ac1bc00000057d179f3c48c4c97e911341edec0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2038a009306383041011001043c2e1000000f9cee7bfd5ecb0b32c1fb102c3c0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2024224f10804c2c340122ff3b5519000000eb63bfb10cd6b439525e2edb3fc0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201c51890c31c3080a020a6b1ae34000000095bbd8946f5aa5e5f3e5eeb8a640':
        (SmartHomeDevice smartHomeDevice) =>
            DishWasherMoreViewModel(device: smartHomeDevice),
    '201c51890c31c3080a02bec3286be5000000c46d7ba6333737be752095119440':
        (SmartHomeDevice smartHomeDevice) =>
            DishWasherViewModel(device: smartHomeDevice),
    '111c120024000810060500418001840000000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceAlarmCancelKeyViewModel('506000', device: smartHomeDevice),
    '111c120024000810080300718000674200000000000000000000000000000000':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201872470460c27414106fdfe61f88a1bb9dcfc564062d3d48b3a25ce58e9240':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2010f0c21073872001210061800347460000000f0000000000000000000f00c0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //空调
    '201c12002400081003111169c9cf440356ece0a56927eec70c9d37e3391ea940':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //热泵KD40_120_AE3U
    '201c1200240008102001619e104261ceb057c22bb7f2094efc2dbd0a32c02140':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //ZQD100FH701
    '2004e3c43cf24f380b014d4631eb724dfcb72262c9addcdca918c8023f3dcfc0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //电磁灶
    '2010f0c2107387202805e6d04ea2f142a88685a946339354ec4dc44435bf93c0':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //电视
    'f#1': (SmartHomeDevice smartHomeDevice) =>
        TvCardViewModel(device: smartHomeDevice),
    //可视门铃
    '201c80c70c50031c12028e6aa04fb10000002663f244a9812578044239cb0840':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //消毒柜：型号ZQD100F-C7MAGU1
    '201c10c7141202100b12ac65d9e67500000073c6829dfd0aa151fcb862682640':
        (SmartHomeDevice smartHomeDevice) =>
            SterilizerCardViewModel(device: smartHomeDevice),
    //支持实时离在线的声光报警器
    '201c80c70c50031c11070cf5b6d28e000000c7fdd72b703f48ab732fd648b840':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceSupportAlarmFalseViewModel(device: smartHomeDevice),
    //智能面板 HED-H2CP4-5D黑
    '201c80c70c50031c14206d0f8fe61b000000a29e81673b0d5ddc44a89189d240':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //蒸箱
    '201c10c7141202101a01dfa20f2e9e000000d7982155dbda7b78c559cfd5c840':
        (SmartHomeDevice smartHomeDevice) =>
            SteamerCardViewModel(device: smartHomeDevice),
    '201c10c7141202101a0166fd894f0f000000d08870e2d6ba44a4f27e8e3ba840':
        (SmartHomeDevice smartHomeDevice) =>
            SteamerCardViewModel(device: smartHomeDevice),
    '201c10c7141202101a0139500b220a000000003a94d8c7f2dff1c819ff90df40':
        (SmartHomeDevice smartHomeDevice) =>
            SteamerCardViewModel(device: smartHomeDevice),
    //浴霸
    '2054a0d610c6d2103401b1bee6e897000000c58ff5b427fc01a61813efdade40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '2054a0d610c6d21034016460c8c64b000000eaa34b57c8467c4426062d710d40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //管线机
    '201461071080c014220a01904002430000000000010000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //咖啡机
    '201c10c70400457428017e1e6d900500000031c6f97125fd4392cecc3e5bf840':
        (SmartHomeDevice smartHomeDevice) =>
            CoffeeMachineViewModel(device: smartHomeDevice),
    //485地暖设备-全屋
    '201c80c70c50031c250112017eb51b0000008748e96cb416e089b53158357640':
        (SmartHomeDevice smartHomeDevice) =>
            UnderFloorHeatingPanel(device: smartHomeDevice),
    //485新风设备
    '201c80c70c50031c24018e77b297f7000000bcc846db62c08c43597cf81d6440':
        (SmartHomeDevice smartHomeDevice) =>
            FanTemperature485PanelViewModel(device: smartHomeDevice),
    //垃圾处理器
    '201c10c7141202102c0193545df1df000000781e2f862fcaf65bcc26231df440':
        (SmartHomeDevice smartHomeDevice) =>
            GarbageDisposalViewModel(device: smartHomeDevice),
    //扫地机器人
    '2054a0cc113541202702db4722b49d0000000ae32d3f75dc10890337f6047940':
        (SmartHomeDevice smartHomeDevice) =>
            SweepingRobotCardViewModel(device: smartHomeDevice),
    //推窗器
    '2008610800820324141707642e6f8600000099535918772cb5c2bdeb6f9c2040':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //HPR-P501SU1 料理机
    '201c10c704004574280903f7dd40b60000008d5a6ab9e31292e9e023cf36c040':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //声光同步盒
    '201c80c70c50031c0f09ce2dfc8847000000296a982986c8ae6d805777768e40':
        (SmartHomeDevice smartHomeDevice) =>
            SoundLightSyncBoxViewModel(device: smartHomeDevice),
    //声光同步灯
    '201c80c70c50031c10111269b9b9d6000000150b042b9f57a7910ee5fac92740':
        (SmartHomeDevice smartHomeDevice) =>
            AcoustoLightViewModel(device: smartHomeDevice),
    //环境检测仪
    '201c80c70c50031c29011269b9b9d6000000d972131a5a80a142d64dda9a8a40':
        (SmartHomeDevice smartHomeDevice) =>
            EnvironmentalDetectorViewModel(device: smartHomeDevice),
    // 湿度传感器
    '201c80c70c50031c29042c73d37d5100000019a297df973418b68c882b369540':
        (SmartHomeDevice smartHomeDevice) =>
            EnvironmentalDetectorViewModel(device: smartHomeDevice),
    '201c80c70c50031c290168d0071bf50000003ba6089c8ed5fb2994a802944140':
        (SmartHomeDevice smartHomeDevice) =>
            EnvironmentalDetectorViewModel(device: smartHomeDevice),
    //新风面板
    '201c80c70c50031c24012c30f5aa4500000083dc996da637206224b160bc1e40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),

    //--------------开关--------------
    //贝多芬智能开关CET-ECDWN2-U5  编码：NE07X5M06  二键开关
    '201c80c70c50031c14117caf416b77000000307964c73340b3a36e7bd7561740':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchTwoKeyViewModel(device: smartHomeDevice),
    //贝多芬智能开关CET-ECDWN4-U5  编码：NE07X2M06  四键开关
    '201c80c70c50031c14117caf416b77000000b26e391e7ebf34a9932ca16c9340':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchFourKeyViewModel(device: smartHomeDevice),
    //智能开关(魔方开关) 型号：HED-H2MTN1-U5深砂灰 编码：NE0786D00  一键开关
    '201c80c70c50031c1411c306373ad1000000a75fd95709cef3ce2959287bd340':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchOneKeyViewModel(device: smartHomeDevice),
    //智能开关(魔方开关) 型号：HED-H2MTN2-U5深砂灰 编码：NE0786D00  二键开关
    '201c80c70c50031c1411c306373ad100000071095cf51b1a29ffa312ddf37e40':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchTwoKeyViewModel(device: smartHomeDevice),
    //智能开关(魔方开关) 型号：HED-H2MTN3-U5深砂灰  编码：NE0788D00  三键开关
    '201c80c70c50031c1411c306373ad10000006345618e96448aab1bf9db91a140':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchThreeKeyViewModel(device: smartHomeDevice),
    //智能开关(魔方开关) 型号：HED-H2MTN4-U5深砂灰  编码：NE0789D00 四键智能开关
    '201c80c70c50031c1411c306373ad100000028247a22597dc9b9301fb1585640':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchFourKeyViewModel(device: smartHomeDevice),
    //智能开关 型号：HET-H2PWN1-U5  编码：NE07XMD01    一键开关
    '201c80c70c50031c141127f9da65420000003abd24679a7c329580882fda8b40':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchOneKeyViewModel(device: smartHomeDevice),
    //智能开关 型号：HET-H2PWN2-U5 编码：NE07XND01     二键开关
    '201c80c70c50031c141127f9da6542000000eba9ed4d6cd4f9490128bd770140':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchTwoKeyViewModel(device: smartHomeDevice),
    //智能开关 型号：HET-H2PWN3-U5  编码：NE07XPD01     三键开关
    '201c80c70c50031c141127f9da654200000079287b58640ac91fe1c91fb35440':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchThreeKeyViewModel(device: smartHomeDevice),
    //智能开关 型号：HET-H2PWN4-U5  编码：NE07XLD02 四键智能开关
    '201c80c70c50031c141112ba3a8bf2000000e160a7511484e141e4f1f4292940':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchFourKeyViewModel(device: smartHomeDevice),
    '201c80c70c50031c1411ef7954590100000004c0b4877c1913cd3985e95bed40':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchOneKeyViewModel(device: smartHomeDevice),
    //智能开关（触摸开关）  型号：HET-H2P7N2-U5   编码：NE07XQD00  两键触控开关
    '201c80c70c50031c1411ef79545901000000fd1856b8d628ddf59b05d0cbef40':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchTwoKeyViewModel(device: smartHomeDevice),
    //智能开关（触摸开关）  型号：HET-H2P7N3-U5   编码：NE07XRD00  三键触控开关
    '201c80c70c50031c1411ef795459010000009717f4330a0ec7ad45571b1cfb40':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchThreeKeyViewModel(device: smartHomeDevice),
    '201c80c70c50031c1411ee1382da770000006293b9c13246019edfae0bb9f340':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchTwoKeyViewModel(device: smartHomeDevice),
    //四键开关 型号：CET-12DWN4-U5  编码：NE07XHD02   四键智能开关
    '201c80c70c50031c1411ee1382da77000000f366d47f6678be2acbc3de457a40':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchFourKeyViewModel(device: smartHomeDevice),
    //智能开关  型号：HET-10DWN1-U5   编码： 一键开关
    '201c80c70c50031c1411768ace1df6000000acc34474c20c588a525f9c738940':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchOneKeyViewModel(device: smartHomeDevice),
    //智能开关  型号：HET-10DWN2-U5   编码：  二键开关
    '201c80c70c50031c1411768ace1df6000000f07b7205483a6544ccac0b376440':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchTwoKeyViewModel(device: smartHomeDevice),
    //智能开关  型号：HET-10DWN3-U5   编码： 三键开关
    '201c80c70c50031c1411768ace1df600000071fec629997d36befd3dae881240':
        (SmartHomeDevice smartHomeDevice) =>
            SwitchThreeKeyViewModel(device: smartHomeDevice),
    //易来开关 ----------------- start
    '200051c2147108001411fe6d76f0cb0000008363418a6741eab19baa9a5cec40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '200051c2147108001411fe6d76f0cb000000d391df526185d397a5a55f78da40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '200051c2147108001411fe6d76f0cb000000f51c9a7eede894c87474c1074440':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '200051c2147108001411fe6d76f0cb000000508528840a92c9abb1d39d0d8140':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '200051c2147108001411fe6d76f0cb000000de94027ee1efb91b079f37940540':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '200051c214710800141182c7f391b2000000461e0e46968afc873dab2f702b40':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    '201c80c70c50031c2401f4573fce380000002fb0422ee6e7f9662c93d6c12140':
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice),
    //易来开关 ----------------- end
    //------------- 斐雪派克洗碗机 ---------------------------------
    '201c51890c31c3080a0201218001045341000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            SingleWasherFxViewModel(device: smartHomeDevice),
    '201c51890c31c3080a0351993700000000000000160000000000000000001140':
        (SmartHomeDevice smartHomeDevice) =>
            SingleWasherFxViewModel(device: smartHomeDevice),
    '201c51890c31c3080a0351993800000000000000170000000000000000001140':
        (SmartHomeDevice smartHomeDevice) =>
            SingleWasherFxViewModel(device: smartHomeDevice),
    //智能音箱
    '2054a0000426cb243b0198d5760c1d000000bd121b216b07e45bcaefe67fcc40':
        (SmartHomeDevice smartHomeDevice) =>
            VoiceBoxCardViewModel(device: smartHomeDevice),
    '2054a0000426cb243b01b351503e7e0000009ebe6edafddeb12ed7637cacda40':
        (SmartHomeDevice smartHomeDevice) =>
            VoiceBoxCardViewModel(device: smartHomeDevice),
    '2054a0000426cb243b01be99fbb6b2000000ae73dd8d7e728e323eb8a052b140':
        (SmartHomeDevice smartHomeDevice) =>
            VoiceBoxCardViewModel(device: smartHomeDevice),
    '2054a0d610c6d2103b01b2d8cd5716000000d059588c29bb04b8657e80fdb040':
        (SmartHomeDevice smartHomeDevice) =>
            VoiceBoxCardViewModel(device: smartHomeDevice),
    // 窗帘
    '14#13': (SmartHomeDevice smartHomeDevice) =>
        CurtainCardViewModel(device: smartHomeDevice),
    '201c120024000810140200000000000000000000000000000000000000000040':
        (SmartHomeDevice smartHomeDevice) =>
            CurtainCardViewModel(device: smartHomeDevice),
    '201c80c70c50031c1413c001c47138000000ec24bf805b4531c836bfe2d0b740':
        (SmartHomeDevice smartHomeDevice) =>
            CurtainCardViewModel(device: smartHomeDevice),
    // 扫地机
    '27#2': (SmartHomeDevice smartHomeDevice) =>
        SweepingRobotCardViewModel(device: smartHomeDevice),
    '27#3': (SmartHomeDevice smartHomeDevice) =>
        SweepingRobotCardViewModel(device: smartHomeDevice),
    '201c80c70c50031c1420dee25c4af2000000318c52aa328d5dfba238f5db0140':
        (SmartHomeDevice smartHomeDevice) =>
            ControlPanel12ViewModel(device: smartHomeDevice),
    '201c80c70c50031c1420accebb5c30000000f3d081b94a90dfb7190af0ddea40':
        (SmartHomeDevice smartHomeDevice) =>
            ControlPanel12ViewModel(device: smartHomeDevice),
    '201c80c70c50031c1420e13180e26100000098d67ff489eca15db755d1fdfa40':
        (SmartHomeDevice smartHomeDevice) =>
            ControlPanelAndroid4ViewModel(device: smartHomeDevice),
    '201c80c70c50031c14202c2a0dab2a0000007364ad3eb0f83884fdbd84781840':
        (SmartHomeDevice smartHomeDevice) =>
            ControlPanelLinux4ViewModel(device: smartHomeDevice),
  };

  static DeviceCardViewModel viewModelFromSmartHomeDevice(
      SmartHomeDevice smartHomeDevice) {
    String bigClass = smartHomeDevice.basicInfo.bigClass;
    final String middleClass = smartHomeDevice.basicInfo.middleClass;

    if (isDeviceLightAggregation(smartHomeDevice.basicInfo.deviceId)) {
      // 聚合灯
      return AggregationLightViewModel(device: smartHomeDevice);
    }
    if (isDeviceCurtainAggregation(smartHomeDevice.basicInfo.deviceId)) {
      // 聚合窗帘
      return AggregationCurtainViewModel(device: smartHomeDevice);
    }
    if (isEnvAgg(smartHomeDevice.basicInfo.deviceId)) {
      // 聚合环境
      return AggEnvViewModel(device: smartHomeDevice);
    }

    if (isCameraAgg(smartHomeDevice.basicInfo.deviceId)) {
      // 摄像机聚合
      return AggregationCameraCardViewModel(device: smartHomeDevice);
    }

    if (isNonNetAgg(smartHomeDevice.basicInfo.deviceId)) {
      // 非网器聚合
      return AggNonNetViewModel(device: smartHomeDevice);
    }

    if (isOfflineAgg(smartHomeDevice.basicInfo.deviceId)) {
      // 长期离线聚合
      return AggOfflineViewModel(device: smartHomeDevice);
    }

    if (AirConditionSupportDevice.airConditionModelList
        .contains(smartHomeDevice.basicInfo.model)) {
      final DeviceCardViewModelBuilder? builder =
          _cardViewModelRelationMap['2'];
      if (builder != null) {
        return builder(smartHomeDevice);
      }
    }

    DeviceCardViewModelBuilder? builder =
        _cardViewModelRelationMap[smartHomeDevice.basicInfo.model];
    if (builder != null) {
      return builder(smartHomeDevice);
    }

    builder = _cardViewModelRelationMap[smartHomeDevice.basicInfo.typeId];
    if (builder != null) {
      return builder(smartHomeDevice);
    }

    builder = _cardViewModelRelationMap['$bigClass#$middleClass'];
    if (builder != null) {
      return builder(smartHomeDevice);
    }

    builder = _cardViewModelRelationMap[bigClass] ??
        (SmartHomeDevice smartHomeDevice) =>
            DeviceCardViewModel(device: smartHomeDevice);
    return builder(smartHomeDevice);
  }

  static Widget deviceCardWidget(DeviceCardViewModel deviceCardViewModel) {
    if (deviceCardViewModel.deviceCardType == DeviceCardType.largeCard) {
      if (deviceCardViewModel.cardType == CardType.cameraCard) {
        if (deviceCardViewModel is CameraDeviceCardViewModel) {
          return Camera(
            deviceCardViewModel.device.basicInfo.deviceId,
            deviceCardViewModel.editComponentViewModel,
            deviceCardViewModel.traceId,
          );
        }
      }
      return DeviceCardLarge(deviceCardViewModel: deviceCardViewModel);
    }
    if (deviceCardViewModel.deviceCardType == DeviceCardType.middleCard) {
      return DeviceCardMiddle(deviceCardViewModel: deviceCardViewModel);
    }
    return DeviceCardSmall(deviceCardViewModel: deviceCardViewModel);
  }

  static Widget otherCardWidget(CardBaseViewModel viewModel) {
    if (viewModel.cardType == CardType.repairCard) {
      return ReportForRepairCard(
          viewModel: viewModel as ReportForRepairCardViewModel);
    }
    if (viewModel.cardType == CardType.addCard) {
      return AddDeviceCard(viewModel: viewModel as AddDeviceCardViewModel);
    }
    if (viewModel.cardType == CardType.newUserPackCard) {
      return NewUserPackWidget(viewModel: viewModel as NewUserPackViewModel);
    }
    return Container();
  }
}
