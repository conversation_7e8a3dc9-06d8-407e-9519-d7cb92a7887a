import 'package:device_utils/log/log.dart';
import 'package:family/device_brief_model.dart';
import 'package:family/device_manage_model.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/edit/edit_presenter/edit_presenter_manager.dart';
import 'package:smart_home/edit/util/edit_constant.dart';

import '../../store/smart_home_store.dart';
import '../edit_card_base_info_model.dart';
import '../store/edit_action.dart';
import '../util/edit_manager.dart';

class DeleteDevicePresenter {
  static String primaryDialogContent = '';
  static List<EditDeviceCardInfo> editDeviceList = <EditDeviceCardInfo>[];

  static void showDialog(BuildContext context) {
    _setEditDeviceList();
    _setPrimaryDialogContent();

    EditPresenterManager.dialogs.showDoubleBtnDialog(
      context: context,
      title: EditConstant.deleteTitle,
      content: primaryDialogContent,
      confirmText: '删除',
      confirmBtnType: ButtonType.warning,
      confirmCallback: () {
        EditPresenterManager.onClickDone(
            saveHomeCardCallback: ({bool? isSuccess}) {
          _handleDeleteDevice();
        });
      },
    );
  }

  static void _setPrimaryDialogContent() {
    bool hasPrimaryDevice = false;
    for (int i = 0; i < editDeviceList.length; i++) {
      final EditDeviceCardInfo info = editDeviceList[i];
      if (info.deviceRole == deviceRolePrimary) {
        hasPrimaryDevice = true;
        break;
      }
    }
    primaryDialogContent = hasPrimaryDevice
        ? EditConstant.deletePrimaryContent
        : EditConstant.deleteConfirmContent;
  }

  static void _setEditDeviceList() {
    editDeviceList = <EditDeviceCardInfo>[];
    if (smartHomeStore.state.editState.selectedCardsList.isNotEmpty) {
      editDeviceList.addAll(smartHomeStore.state.editState.selectedCardsList);
    }
  }

  static void _handleDeleteDevice() {
    final FamilyModel? familyModel = Family.getCurrentFamilySync();
    final String curFamilyId = familyModel?.familyId ?? '';
    final List<String> deviceIdList = <String>[];
    editDeviceList.forEach((EditDeviceCardInfo element) {
      deviceIdList.add(element.id);
    });

    Family.unbindDeviceFromFamily(curFamilyId, deviceIdList)
        .then((DeviceManageModel value) {
      DevLogger.info(
          tag: SmartHomeEditManager.tag,
          msg: 'DeleteDevicePresenter _handleDeleteDevice $value');
      if (value.failureDevices.isEmpty) {
        ToastHelper.showToast(EditConstant.deleteDeviceSuccess);
      } else if (value.successDevices.isEmpty) {
        ToastHelper.showToast(EditConstant.deleteDeviceFail);
      } else {
        final List<String> successDeviceNameList = <String>[];
        value.successDevices.forEach((DeviceBriefModel element) {
          successDeviceNameList.add(element.deviceName);
        });
        final List<String> failureDeviceNameList = <String>[];
        value.failureDevices.forEach((DeviceBriefModel element) {
          failureDeviceNameList.add(element.deviceName);
        });
        ToastHelper.showToast(
            '${failureDeviceNameList.length}个设备${EditConstant.deleteDeviceFail}');
      }
    }).catchError((dynamic error) {
      DevLogger.error(
          tag: 'DeleteDevicePresenter',
          msg: '_handleDeleteDevice err = $error');
      ToastHelper.showToast(EditConstant.deleteDeviceFail);
    });
  }
}
