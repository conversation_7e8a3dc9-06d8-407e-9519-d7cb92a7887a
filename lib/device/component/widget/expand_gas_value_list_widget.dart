/*
 * 描述：燃热温控组件
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/debounce.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component/widget/click_effect_circular_widget.dart';

import '../../../common/constant.dart';
import '../../../store/smart_home_store.dart';
import '../view_model/expand_gas_value_list_view_model.dart';

class ExpandGasValueListWidget extends StatefulWidget {
  const ExpandGasValueListWidget({super.key, required this.viewModel});

  final ExpandGasValueListViewModel viewModel;

  @override
  State<ExpandGasValueListWidget> createState() => _FlexValueListWidgetState();
}

class _FlexValueListWidgetState extends State<ExpandGasValueListWidget> {
  bool _manual = false;
  bool _preEnable = true;
  bool _nextEnable = true;
  bool _triggerAlarm = true;
  final double _alarmValue = 50;
  final String _safetyLockToastText = '高温安全锁已开启';
  int _currentIndex = 0;
  String _currentValue = '--';
  String _unit = '';
  List<String> stringValueList = <String>[];

  final Debouncer _clickDebouncer = Debouncer(milliseconds: 500);
  final Debouncer _setStateDebouncer = Debouncer(milliseconds: 2000);

  final Dialogs _dialogs = Dialogs();

  @override
  void dispose() {
    _clickDebouncer.dispose();
    _setStateDebouncer.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _updateDataState();
  }

  void _updateDataState() {
    if (widget.viewModel.valueList.isNotEmpty) {
      _unit = widget.viewModel.unit;
      stringValueList = widget.viewModel.valueList
          .map((double e) => _tempDoubleToString(e))
          .toList();
      final int _index =
          widget.viewModel.valueList.indexOf(widget.viewModel.currentValue);
      _currentIndex = _index == -1 ? 0 : _index;
      _currentValue = _tempDoubleToString(widget.viewModel.currentValue);
      if (_currentIndex == 0) {
        _preEnable = false;
      }
      if (_currentIndex == widget.viewModel.valueList.length - 1 ||
          _isSafetyLockStatusOn()) {
        _nextEnable = false;
      }
    } else {
      _unit = '';
    }
  }

  // 显示温度转为String且小数部分为0时不显示小数部分
  String _tempDoubleToString(double temp) {
    return temp % 1 == 0 ? temp.toInt().toString() : temp.toStringAsFixed(1);
  }

  // 高温安全锁开启校验
  bool _isSafetyLockStatusOn() {
    return widget.viewModel.currentValue >= _alarmValue &&
        widget.viewModel.safetyLockStatus;
  }

  @override
  void didUpdateWidget(covariant ExpandGasValueListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!_manual) {
      _updateDataState();
      _updateBtnState();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: widget.viewModel.expandFlex,
      child: Opacity(
          opacity: (widget.viewModel.enable &&
                  !widget.viewModel.egwOn &&
                  !smartHomeStore.state.isEditState)
              ? ComponentOpacity.enable
              : ComponentOpacity.disable,
          child: GestureDetector(
            onTap: () {},
            child: Container(
              decoration: BoxDecoration(
                color: AppSemanticColors.background.secondary,
                borderRadius: BorderRadius.circular(12),
              ),
              height: 52,
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  _btnWidget(
                      enable: widget.viewModel.enable &&
                          _preEnable &&
                          !smartHomeStore.state.isEditState,
                      icon: widget.viewModel.preValueIcon,
                      packageName: SmartHomeConstant.package,
                      clickCallback: () {
                        if (!widget.viewModel.enable &&
                            widget.viewModel.clickCallback != null) {
                          widget.viewModel.clickCallback?.call(context);
                          return;
                        }
                        _manual = true;
                        if (!_preEnable) {
                          ToastHelper.showToast(
                              widget.viewModel.comeToFirstAlertMsg);
                        } else {
                          _valueChange(true);
                        }
                        _setStateDebouncer.run(() {
                          setState(() {
                            _manual = false;
                            _updateDataState();
                            _updateBtnState();
                          });
                        });
                      }),
                  RichText(
                    maxLines: 1,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    text: SmartHomeSpan.textSpan(
                      text: _floatValue(_currentValue)
                          ? _intPartValue(_currentValue)
                          : _currentValue,
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: AppSemanticColors.item.primary,
                      children: <InlineSpan>[
                        if (_floatValue(_currentValue))
                          SmartHomeSpan.textSpan(
                              text: _floatPartValue(_currentValue),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: AppSemanticColors.item.primary),
                        const WidgetSpan(
                          child: SizedBox(
                            width: 4,
                          ),
                        ),
                        SmartHomeSpan.textSpan(
                          text: _unit,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppSemanticColors.item.primary,
                        ),
                      ],
                    ),
                  ),
                  _btnWidget(
                      enable: widget.viewModel.enable &&
                          _nextEnable &&
                          !smartHomeStore.state.isEditState,
                      icon: widget.viewModel.nextValueIcon,
                      packageName: SmartHomeConstant.package,
                      clickCallback: () {
                        if (!widget.viewModel.enable &&
                            widget.viewModel.clickCallback != null) {
                          widget.viewModel.clickCallback?.call(context);
                          return;
                        }
                        _manual = true;
                        if (!_nextEnable) {
                          ToastHelper.showToast(_isSafetyLockStatusOn()
                              ? _safetyLockToastText
                              : widget.viewModel.comeToEndAlertMsg);
                        } else {
                          _valueChange(false);
                        }
                        _clickDebouncer.run(() {
                          widget.viewModel.valueChangeCallback
                              ?.call(context, _currentValue, _currentIndex);
                        });
                        _setStateDebouncer.run(() {
                          setState(() {
                            _manual = false;
                            _updateDataState();
                            _updateBtnState();
                          });
                        });
                      }),
                ],
              ),
            ),
          )),
    );
  }

  Widget _btnWidget(
      {required bool enable,
      required String icon,
      required String packageName,
      required void Function() clickCallback}) {
    return GestureDetector(
      onTap: () {
        clickCallback();
      },
      child: SizedBox(
        width: 52,
        height: double.infinity,
        child: Opacity(
          opacity: enable ? ComponentOpacity.enable : ComponentOpacity.disable,
          child: ClickEffectCircularWidget(
            enable: enable,
            isOn: false,
            child: Align(
              child: Image.asset(
                icon,
                package: packageName,
                width: 16,
                height: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool _floatValue(String value) {
    return value.contains('.');
  }

  String _intPartValue(String value) {
    final int index = value.indexOf('.');
    return value.substring(0, index);
  }

  String _floatPartValue(String value) {
    final int index = value.indexOf('.');
    return value.substring(index);
  }

  void _valueChange(bool pre) {
    if (pre) {
      /// -
      final String preValue = stringValueList[_currentIndex - 1];
      final double doublePreValue = double.parse(preValue);
      if (doublePreValue < _alarmValue) {
        _triggerAlarm = false;
      }
      if (widget.viewModel.egwOn) {
        // E感温提示
        _egwAlarm(callback: () {
          _setPreValue();
        });
        return;
      }
      _setPreValue();
    } else {
      /// +
      // 1 燃烧预警50不可操作弹窗(非浴缸注水检查)
      // 2 E感温提示
      // 3 >50度 弹窗提醒
      final String nextValue = stringValueList[_currentIndex + 1];
      final double doubleNextValue = double.parse(nextValue);
      if (doubleNextValue < _alarmValue) {
        _triggerAlarm = false;
      }
      if (doubleNextValue > _alarmValue && widget.viewModel.showBurningAlarm) {
        // 非浴缸注水检查
        _dialogs.showSingleBtnDialog(
          context: context,
          title: '',
          content: '当前水温过高，为防止烫伤，不能升温。若仍需调高温度，请关好水龙头后再进行调节。',
          callback: () {},
        );
        return;
      } else if (widget.viewModel.egwOn) {
        // E感温提示
        _egwAlarm(callback: () {
          _setNextValue();
          if (_isHigh50Alarm(doubleNextValue)) {
            _triggerAlarm = true;
          }
        });
        return;
      } else if (!_triggerAlarm && _isHigh50Alarm(doubleNextValue)) {
        // >50度 弹窗提醒
        _dialogs.showDoubleBtnDialog(
          context: context,
          title: '',
          content: '水温将要超过${_alarmValue.toInt()}℃，是否继续增加？',
          confirmText: '继续调温',
          confirmCallback: () {
            _triggerAlarm = true;
            _setNextValue();
          },
        );
        return;
      }
      _setNextValue();
    }
  }

  bool _isHigh50Alarm(double temp) {
    return temp > _alarmValue;
  }

  void _egwAlarm({required void Function() callback}) {
    _dialogs.showDoubleBtnDialog(
      context: context,
      title: '确认继续调节温度？',
      content: '将自动关闭${widget.viewModel.egwTitle}功能',
      confirmText: '继续调温',
      confirmCallback: callback,
    );
  }

  void _setNextValue() {
    _currentIndex += 1;
    if (_currentIndex < widget.viewModel.valueList.length) {
      _currentValue = stringValueList[_currentIndex];
      setState(() {
        _updateBtnState();
      });
      _valueChangeCallback();
    }
  }

  void _setPreValue() {
    _currentIndex -= 1;
    if (_currentIndex >= 0) {
      _currentValue = stringValueList[_currentIndex];
      setState(() {
        _updateBtnState();
      });
      _valueChangeCallback();
    }
  }

  void _valueChangeCallback() {
    _clickDebouncer.run(() {
      widget.viewModel.valueChangeCallback
          ?.call(context, _currentValue, _currentIndex);
    });
    _setStateDebouncer.run(() {
      setState(() {
        _manual = false;
      });
    });
  }

  void _updateBtnState() {
    if (_currentIndex == 0) {
      _preEnable = false;
    } else {
      _preEnable = true;
    }
    if (_currentIndex == widget.viewModel.valueList.length - 1 ||
        _isSafetyLockStatusOn()) {
      _nextEnable = false;
    } else {
      _nextEnable = true;
    }
  }
}
