import 'package:connectivity/connectivity.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart' show ToastHelper;
import 'package:library_widgets/common/util.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_manager.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../aggregation_detail/widgets/agg_offline_detail_list.dart';
import '../aggregation_setting/util/aggregation_setting_constant.dart';

class AggOfflineViewModel extends DeviceCardViewModel {
  AggOfflineViewModel({required super.device});

  Set<String> longOfflineDeviceIds = <String>{};

  String status() => longOfflineDeviceIds.isEmpty
      ? '暂无设备'
      : '${longOfflineDeviceIds.length}个设备';

  @override
  String get unionMiddleCardStatus => status();

  @override
  String get unionSmallCardStatus => status();

  @override
  String get deviceName => AggregationDeviceConstant.aggOfflineTitle;

  @override
  bool get deviceOffline => false;

  @override
  bool get supportLargeCard => false;

  @override
  String get deviceIcon => 'assets/images/agg_offline_icon.webp';

  @override
  bool get isDeviceIconAsset => true;

  @override
  int get deviceCount => longOfflineDeviceIds.length;

  /// 卡片点击事件处理
  @override
  void cardClick(BuildContext? context, {String? jumpUrl}) {
    gioTrack(AggregationDeviceConstant.cardClickGio, <String, dynamic>{
      'sourceName': AggregationSettingManager.getSourceNameById(deviceId),
      'value': '进详情页'
    });
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (ConnectivityResult.none == result) {
        ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      } else {
        try {
          EditPresenterManager.changePageWithHomeAndAgg(EditPageType.offline);
          Navigator.push(
              context!,
              MaterialPageRoute<AggOfflineDetailList>(
                  builder: (BuildContext context) => AggOfflineDetailList(
                        aggregationId: deviceId,
                      )));
        } catch (e) {
          DevLogger.error(
            tag: 'smart_home',
            msg: 'show aggOffline detail page failed $e',
          );
        }
      }
    });
  }
}
