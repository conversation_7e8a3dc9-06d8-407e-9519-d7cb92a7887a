import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_action.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_camera_card_wrapper.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/presenter/camera_presenter.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_constant.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../../store/smart_home_state.dart';
import '../../device_info_model/smart_home_device.dart';
import '../../device_view_model/card_base_view_model.dart';
import '../aggregation_card/util/aggregation_device_util.dart';

// 筛选非网器聚合设备
void filterAggCameraDeices(
    Map<String, CardBaseViewModel> tmpAllCardViewModelMap,
    SmartHomeDevice smartHomeDevice,
    Set<String> deviceIds) {
  final String deviceId = smartHomeDevice.basicInfo.deviceId;
  final String parentId = smartHomeDevice.basicInfo.aggregationParentId;
  if (isCameraAgg(parentId)) {
    if (tmpAllCardViewModelMap[deviceId] is! DeviceCardViewModel) {
      return;
    }
    deviceIds.add(deviceId);
  } else if (deviceIds.contains(deviceId)) {
    deviceIds.remove(deviceId);
  }
}

// 这块整理聚合camera缓存数据
void updateAggCameraDeviceCard(
    Map<String, CardBaseViewModel> tmpAllCardViewModelMap,
    Set<String> deviceIds,
    Store<SmartHomeState> store) {
  final String aggNonNetDeviceId =
      '${AggregationSettingConstant.camera_id}${store.state.familyState.familyId}';
  final CardBaseViewModel? aggCameraViewModel =
      tmpAllCardViewModelMap[aggNonNetDeviceId];
  if (aggCameraViewModel != null) {
    store.dispatch(UpdateCameraAggListAction(true, deviceIds));
    CameraAggPresenter.getCameraAggSortedList();
  }
}

bool updateAggCameraDevice(
    SmartHomeDevice smartHomeDevice, SmartHomeState state) {
  final String id =
      '${AggregationSettingConstant.camera_id}${state.familyState.familyId}';
  if (smartHomeDevice.basicInfo.aggregationParentId == id) {
    return true;
  }
  return false;
}

void commitUpdateAggCameraDevices(SmartHomeState state,
    {bool isFromDeviceReducer = true}) {
  final String id =
      '${AggregationSettingConstant.camera_id}${state.familyState.familyId}';
  final AggregationCameraCardViewModel? vm = state
      .deviceState.allCardViewModelMap[id] as AggregationCameraCardViewModel?;
  int offlineCount = 0;
  int totalCount = 0;
  int sleepCount = 0;
  int onlineCount = 0;
  if (vm != null) {
    final List<String> invalidList = <String>[];
    state.aggregationState.aggregationCameraViewModel.cameraList
        .forEach((String element) {
      bool flag = false;
      if (state.deviceState.allCardViewModelMap[element]
          is DeviceCardViewModel) {
        final DeviceCardViewModel? cameraVM = state
            .deviceState.allCardViewModelMap[element] as DeviceCardViewModel?;
        if (cameraVM != null) {
          flag = true;
          totalCount++;
          if (cameraVM.device.onlineState ==
                  SmartHomeDeviceOnlineState.offline ||
              cameraVM.device.onlineState ==
                  SmartHomeDeviceOnlineState.onlineNotReady) {
            offlineCount++;
          } else if (cameraVM
                  .device.attributeMap[CameraConstant.sleepAttr]?.value ==
              SmartHomeConstant.deviceAttrTrue) {
            sleepCount++;
          } else {
            onlineCount++;
          }
        }
      }
      if (!flag) {
        invalidList.add(element);
      }
    });
    if (invalidList.isNotEmpty) {
      state.aggregationState.aggregationCameraViewModel.cameraList
          .removeWhere((String element) => invalidList.contains(element));
    }
  }
  vm?.updateInfo(onlineCount, totalCount, offlineCount, sleepCount);
}
