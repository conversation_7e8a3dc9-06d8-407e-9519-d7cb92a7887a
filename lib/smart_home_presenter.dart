import 'dart:async';

import 'package:connectivity/connectivity.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:plugin_device/model/device_msg_model.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:smart_home/cache/model/main_params_model.dart';
import 'package:smart_home/cache/store/cache_action.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_presenter.dart';
import 'package:smart_home/device/fridge_foodnums/fridge_foodnum_presenter.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/navigator/family/family_change_presenter.dart';
import 'package:smart_home/navigator/family/widget/family_pop_dialog.dart';
import 'package:smart_home/offline_gio/offline_gio_config.dart';
import 'package:smart_home/scene/scene_presenter.dart';
import 'package:smart_home/scene/switch/switch_presenter.dart';
import 'package:smart_home/user/user_action.dart';
import 'package:smart_home/whole_house/device_consumables/device_consumables_presenter.dart';
import 'package:smart_home/whole_house/whole_house_presenter.dart';
import 'package:storage/storage.dart';
import 'package:uimessage/event_definition/common_envent.dart';
import 'package:uimessage/uimessage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';
import 'package:wash_device_manager/wash_device_manager.dart';

import 'common/constant_gio.dart';
import 'common/smart_home_util.dart';
import 'device/device_guide/device_guide_widget.dart';
import 'discover/discover_presenter.dart';
import 'edit/edit_presenter/edit_presenter_manager.dart';
import 'edit/store/edit_action.dart';
import 'edit/util/edit_manager.dart';
import 'navigator/family/store/family_action.dart';
import 'offline_gio/offline_gio_track.dart';
import 'pack_gift/giftpack_presenter.dart';
import 'store/smart_home_action.dart';
import 'store/smart_home_store.dart';
import 'time_cost_analysis/analysis_presenter.dart';

class SmartHomePresenter {
  StreamSubscription<BatchDeviceListChangedEvent>? _deviceListChangeListener;
  StreamSubscription<UserLogoutMessage>? _userLogoutListener;
  StreamSubscription<ConnectivityResult>? _networkListener;
  StreamSubscription<PushMessage>? _pushMessage;
  StreamSubscription<FamilyMapUpdateComplete>? _familyMapListener;
  StreamSubscription<BackTopRefreshMessage>? _backTopListener;
  StreamSubscription<CurrentFamilyUpdateComplete>? _currentFamilyListener;
  StreamSubscription<dynamic>? _familyDetailListener;
  StreamSubscription<dynamic>? _roomListChangeListener;
  StreamSubscription<UserLoginSuccess>? _userLoginSuccessListener;
  WholeHousePresenter wholeHousePresenter = WholeHousePresenter();
  ScenePresenter scenePresenter = ScenePresenter();
  DeviceConsumablesPresenter consumablesPresenter =
      DeviceConsumablesPresenter();
  static ItemScrollController? itemScrollController;
  static PageController? smartHomePageController;
  ScrollController? extendScrollController;
  bool _showBackTopBtn = false;
  static const int _showRocketHeight = 250;
  static const int _smartHomeIndex = 0;
  static const int _scrollAnimationDurationMs = 200;
  static const String _smartHomePresenter = 'SmartHomePresenter';

  void initSmartHomeScrollController(ScrollController extendController,
      PageController? pageController, ItemScrollController? itemController) {
    extendScrollController = extendController;
    smartHomePageController = pageController;
    itemScrollController = itemController;
  }

  Future<void> initAppData(
      {required Map<String, String> mainParamsMap,
      required bool isFirstScreen}) async {
    Connectivity()
        .checkConnectivity()
        .then((ConnectivityResult connectivityResult) {
      final bool isNetAvailable = connectivityResult != ConnectivityResult.none;
      smartHomeStore.dispatch(UpdateNetworkStateAction(isNetAvailable));
    });
    final MainParamsModel mainParams = MainParamsModel.fromJson(mainParamsMap);
    String familyId = mainParams.familyId;
    String familyName = mainParams.familyName;
    bool isLogin = mainParams.isLogin;
    int? memberType;

    if (isFirstScreen) {
      smartHomeStore.dispatch(UpdateAppCacheAction(mainParams));
    } else {
      try {
        final LoginStatus loginStatus = await User.getLoginStatus();
        isLogin = loginStatus.isLogin;
        DevLogger.info(
            tag: 'SmartHome',
            msg:
                'initAppData User.getLoginStatus Async-result: loginStatus$isLogin');
      } catch (e) {
        DevLogger.error(
            tag: 'SmartHome', msg: 'initAppData User.getLoginStatus() err:$e');
      }

      try {
        final FamilyModel familyModel = await Family.getCurrentFamily();
        familyId = familyModel.familyId;
        familyName = familyModel.info.familyName;
        memberType = familyModel.memberType;
        DevLogger.info(
            tag: 'SmartHome',
            msg:
                'initAppData Family.getCurrentFamily() Async-result: familyId$familyId, familyName:$familyName');

        final FamilyActionModel familyActionModel = FamilyActionModel(
            familyId: familyId,
            familyName: familyName,
            memberType: memberType,
            isLogin: isLogin);

        FamilyChangePresenter.queryFamilyChangeStatus(familyId);
        smartHomeStore
            .dispatch(UpdateAppCurrentFamilyInfoAction(familyActionModel));
      } catch (e) {
        DevLogger.error(
            tag: 'SmartHome',
            msg: 'initAppData Family.getCurrentFamily() err:$e');
        smartHomeStore.dispatch(UpdateAppCurrentFamilyInfoAction(
            FamilyActionModel(
                familyId: '',
                familyName: '',
                memberType: memberType,
                isLogin: false)));
      }
    }
    if (isLogin) {
      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.loginNewUserPackLocation);
      _fetchFamilyMapData();
      wholeHousePresenter.getWholeHouseData(triggerType: TriggerType.initState);
      SwitchPresenter.querySwitchStatus(familyId);
    } else {
      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.unLoginNewUserPackLocation);
    }

    OfflineGioTrack().updateFamilyId(familyId);
    Future<void>.delayed(const Duration(seconds: 3), () {
      OfflineGIOConfig().init();
    });
    gioTrack(GioConst.launchParams,<String,dynamic>{
      DevListGioEvent.familyId: familyId,
      DevListGioEvent.loginStatus: isLogin,
    });
  }

  static void changeTabbarToInit() {
    smartHomeStore.dispatch(UpdateDeviceTabIndexAction(0));
  }

  static void changeTab(int index) {
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      if (smartHomePageController != null &&
          smartHomePageController!.hasClients) {
        smartHomePageController!.jumpToPage(index);
      }
    });
    DevLogger.info(
        tag: 'get_current_tag',
        msg: 'get_current_tag  current tab index$index');
    if (index == 0) {
      gioTrack(SmartHomeConstant.quickListExposureGio);
    }
  }

  void fetchWholeHouseAndSceneDataInCurFamily(String familyId) {
    scenePresenter.fetchSceneData(familyId);
  }

  /// 查询仪表盘数据
  Future<void> fetchWholeHouseDataInCurFamily(
      {required TriggerType triggerType}) async {
    wholeHousePresenter.getWholeHouseData(triggerType: triggerType);
  }

  void addPluginListeners() {
    // 监听家庭列表变化
    _familyMapListener = UIMessage.sub<FamilyMapUpdateComplete>(
        (FamilyMapUpdateComplete event) async {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg: '__plugin_msg__ UIMessage.FamilyMapUpdateComplete callback');
      gioTrack(GioConst.familyMapUpdateComplete);
      _fetchFamilyMapData();
    });
    // 监听当前家庭变化
    _currentFamilyListener = UIMessage.sub<CurrentFamilyUpdateComplete>(
        (CurrentFamilyUpdateComplete event) async {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg: '__plugin_msg__ UIMessage.CurrentFamilyUpdateComplete callback');

      WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
        extendScrollController?.animateTo(0,
            duration: const Duration(milliseconds: 200),
            curve: Curves.fastOutSlowIn);
      });
      final int traceId = DateTime.now().millisecondsSinceEpoch;
      TimeConsumeStatisticTracker.trace(
          traceId: traceId,
          loc: TraceLocation.t1_2,
          traceType: TraceType.curFamilyChange);
      DevLogger.info(
          tag: _smartHomePresenter,
          msg:
              '__seq__ CurrentFamilyUpdateComplete callback traceId:@$traceId');
      try {
        final FamilyModel familyModel = await Family.getCurrentFamily();

        WashDeviceManager.getInstance().cancelLoadWashDeviceData();
        DevicePresenter.getInstance().unSubscribeDeviceAttribute();

        final bool? isLogin = User.getLoginStatusSync()?.isLogin;
        final FamilyActionModel familyActionModel = FamilyActionModel(
            familyId: familyModel.familyId,
            familyName: familyModel.info.familyName,
            memberType: familyModel.memberType,
            isLogin: isLogin);
        gioTrack(DevListGioEvent.t0CurFamilyChange, <String, dynamic>{
          DevListGioEvent.familyId: familyModel.familyId,
          DevListGioEvent.loginStatus: isLogin,
        });
        smartHomeStore
            .dispatch(UpdateAppCurrentFamilyInfoAction(familyActionModel));
        fetchWholeHouseDataInCurFamily(
            triggerType: TriggerType.currentFamilyChange);

        scenePresenter.fetchSceneData(familyModel.familyId);
        SwitchPresenter.querySwitchStatus(familyModel.familyId);
        FamilyChangePresenter.queryFamilyChangeStatus(familyModel.familyId);
        DevicePresenter.getInstance().getDeviceList(DeviceListRequestModel(
          familyId: familyModel.familyId,
          traceId: traceId,
          traceType: TraceType.curFamilyChange,
          triggerType: DeviceListFetchTriggerType.currentFamilyChanged,
        ));
        OfflineGioTrack().updateFamilyId(familyModel.familyId ?? '');
        DiscoverPresenter.getInstance().getDiscoveredDeviceList();
      } catch (err) {
        TimeConsumeStatisticTracker.trace(
            traceId: traceId,
            loc: TraceLocation.t1_2,
            gioEnable: false,
            traceType: TraceType.curFamilyChange);
      }
    });
    // 监听用户登录状态变化
    _userLoginSuccessListener =
        UIMessage.sub<UserLoginSuccess>((UserLoginSuccess event) {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg: '__plugin_msg__ UIMessage.UserLoginSuccess callback');

      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.loginNewUserPackLocation);
      smartHomeStore.dispatch(UpdateLoginStatusAction(isLogin: true));
      gioTrack(GioConst.userLoginSuccess);
      _fetchFamilyMapData();
    });

    // 退出登陆监听
    _userLogoutListener =
        Message.listen<UserLogoutMessage>((UserLogoutMessage event) {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg: '__plugin_msg__ Message.UserLogoutMessage callback');

      Storage.putStringValue(
          SmartHomeConstant.smartHomeAppBrand, SmartHomeConstant.haier);
      smartHomeStore.dispatch(LogoutAction());
      hideFamilyPopDialog();

      /// 清空仪表盘数据
      wholeHousePresenter.clearWholeHouseData();
      scenePresenter.clearSceneData();
      DevicePresenter.getInstance().clearDeviceList();
      FamilyChangePresenter.isInitFamilyChange = false;
      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.unLoginNewUserPackLocation);
      DevLogger.info(
          tag: 'SmartHomePresenter logout',
          msg: 'SmartHomePresenter logout clear device list');

      gioTrack(GioConst.userLogoutMessage);
    });
    // 网络变化监听
    _networkListener = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) async {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg:
              '__plugin_msg__ Connectivity.onConnectivityChanged callback:$result');

      smartHomeStore.dispatch(
          UpdateNetworkStateAction(result != ConnectivityResult.none));
      if (result != ConnectivityResult.none &&
          smartHomeStore.state.editState.isNeedSaveEditedCardListData) {
        EditPresenterManager.onClickDone();
      }
    });
    // ums推送
    _pushMessage = Message.listen<PushMessage>((PushMessage event) {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg:
              '__plugin_msg__ Message.PushMessage callback, event:${event.messageMap}');

      final dynamic msgName = event.messageMap['msgName'];
      // 场景推送
      if (msgName == 'SCENE_BSM') {
        scenePresenter
            .fetchSceneData(smartHomeStore.state.familyState.familyId);
      }
      // 耗材推送
      if (msgName == 'APP_Dashboard_Consumable') {
        consumablesPresenter
            .fetchDeviceConsumables(smartHomeStore.state.familyState.familyId);
      }

      /// 冰箱食材数变化推送
      if (msgName == FridgeFoodNumPresenter.foodNumPushMessageName) {
        FridgeFoodNumPresenter.parseFoodNumPushMessage(event.messageMap);
      }
    });

    //家庭信息刷新完成
    _familyDetailListener =
        Message.listenWithName('FamilyDetailRefreshed', (dynamic data) {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg: '__plugin_msg__ Message.FamilyDetailRefreshed callback');

      gioTrack(GioConst.familyDetailRefreshed);
      _fetchFamilyMapData();
    });

    //修改房间顺序通知
    _roomListChangeListener = Message.listenWithName(
        'UserCurrentFamilyRoomListChange', (dynamic data) {
      DevLogger.error(
          tag: 'SmartHomePresenter',
          msg: '__plugin_msg__ _roomListChangeListener callback');

      DevicePresenter.getInstance().getDeviceList(DeviceListRequestModel(
        familyId: smartHomeStore.state.familyState.familyId,
        triggerType: DeviceListFetchTriggerType.roomListOrderChanged,
      ));
    });

    _backTopListener = UIMessage.sub<BackTopRefreshMessage>(
        (BackTopRefreshMessage event) async {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg: '__plugin_msg__ UIMessage.BackTopRefreshMessage callback');

      final int? index = event.tabIndex;
      if (index != null && index == _smartHomeIndex) {
        extendScrollController?.animateTo(0,
            duration: const Duration(milliseconds: _scrollAnimationDurationMs),
            curve: Curves.fastOutSlowIn);
      }
    });
  }

  void subscribeDeviceList() {
    UpDevicePlugin.subscribeDeviceListChange(_deviceListChangeCallback)
        .catchError((dynamic error) {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg:
              '__plugin_msg__ UpDevicePlugin.subscribeDeviceListChange error $error');
    });
  }

  void _deviceListChangeCallback() {
    final int traceId = DateTime.now().millisecondsSinceEpoch;
    TimeConsumeStatisticTracker.trace(
        traceId: traceId,
        loc: TraceLocation.t1_1,
        traceType: TraceType.devListChange);
    DevLogger.error(
        tag: _smartHomePresenter,
        msg:
            '__plugin_msg__ UpDevicePlugin.subscribeDeviceListChange callback, beginTime@$traceId');

    // 若当前处于编辑状态则退出编辑模式
    if (smartHomeStore.state.isEditState) {
      smartHomeStore.dispatch(ExitEditStateAction());
    }

    EditPresenterManager.dialogs.closeSmartHomeModalBottomSheet();
    if (SmartHomeEditManager.editDialogContext != null) {
      SmartHomeEditManager.closeEditDialog();
    }

    final FamilyModel? curFamily = Family.getCurrentFamilySync();
    gioTrack(DevListGioEvent.t0DevListChange, <String, String>{
      DevListGioEvent.familyId: curFamily?.familyId ?? '',
    });
    final int getFamilySyncEnd = DateTime.now().millisecondsSinceEpoch;
    DevLogger.info(
        tag: _smartHomePresenter,
        msg:
            '__seq__ UserDeviceListChangeMessage callback, traceId:$traceId, getCurrentFamilySync end:@$getFamilySyncEnd@,curFamilyId:${curFamily?.familyId}, cost:${getFamilySyncEnd - traceId}');
    final String familyId =
        curFamily?.familyId ?? smartHomeStore.state.familyState.familyId;
    if (familyId.isEmpty) {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg:
              '__seq__ UserDeviceListChangeMessage callback, traceId:$traceId, getCurrentFamilySync end family is null, return');
      TimeConsumeStatisticTracker.trace(
          traceId: traceId,
          loc: TraceLocation.t1_1,
          traceType: TraceType.devListChange,
          gioEnable: false);
      return;
    }

    DevicePresenter.getInstance().getDeviceList(DeviceListRequestModel(
      familyId: familyId,
      traceId: traceId,
      triggerType: DeviceListFetchTriggerType.deviceListChanged,
    ));
    wholeHousePresenter.getWholeHouseData(
        triggerType: TriggerType.deviceListChange);
  }

  Future<void> _fetchFamilyMapData() async {
    try {
      final Map<String, FamilyModel> familyMap = await Family.getFamilyMap();
      DevLogger.debug(
          tag: _smartHomePresenter,
          msg:
              '__seq__ _fetchFamilyMapSyncData callback, length:${familyMap.length}');
      smartHomeStore.dispatch(UpdateAppFamilyMapAction(familyMap));
    } catch (e) {
      DevLogger.error(
          tag: _smartHomePresenter,
          msg: '__seq__ _fetchFamilyMapSyncData error $e');
    }
  }

  void handleJumpToUrl(Map<dynamic, dynamic> params) {
    switch (params['tabType']) {
      case 'water':
        goToPageWithDebounce(SmartHomeConstant.waterPage);
      case 'heating':
        goToPageWithDebounce(SmartHomeConstant.heatingPage);
      case 'air':
        goToPageWithDebounce(SmartHomeConstant.airPage);
      case 'whole_house':
        goToPageWithDebounce(SmartHomeConstant.wholeHousePage);
      case 'mine':
        goToPageWithDebounce(SmartHomeConstant.sceneMinePage);
      case 'recommend':
        goToPageWithDebounce(SmartHomeConstant.sceneRecommendPage);
      default:
    }
  }

  // 跳转至聚合引导
  Future<void> checkGuideStatus(BuildContext context) async {
    try {
      final LoginStatus loginStatus = await User.getLoginStatus();
      if (!loginStatus.isLogin) {
        return;
      }

      final UserInfo userInfo = await User.getUserInfo();
      final String guideAgeKey =
          '${SmartHomeConstant.userGuideStatus}${userInfo.userId}';
      final String guideFamilyKey =
          '${SmartHomeConstant.userGuideFamilyStatus}${userInfo.userId}';
      final bool guideAgeStatus = await Storage.getBooleanValue(guideAgeKey);
      if (guideAgeStatus) {
        await jumpFamilyGuide(guideAgeKey, guideFamilyKey);
      } else {
        jumpFullGuide(guideAgeKey, guideFamilyKey);
      }
    } catch (err) {
      DevLogger.error(
          tag: _smartHomePresenter, msg: 'checkGuideStatus error $err');
    }
  }

  void jumpFullGuide(String guideAgeKey, String guideFamilyKey) {
    gioTrack('MB35999');
    Storage.putBooleanValue(guideAgeKey, true);
    Storage.putBooleanValue(guideFamilyKey, true);
    goToPage(SmartHomeConstant.deviceGuideUrl,
        params: <String, dynamic>{ONLY_FAMILY_GUIDE: false});
  }

  Future<void> jumpFamilyGuide(
      String guideAgeKey, String guideFamilyKey) async {
    final bool familyGuideStatus =
        await Storage.getBooleanValue(guideFamilyKey);
    if (!familyGuideStatus) {
      gioTrack('MB35999');
      Storage.putBooleanValue(guideFamilyKey, true);
      goToPage(SmartHomeConstant.deviceGuideUrl);
    }
  }

  void removePluginListeners() {
    _familyMapListener?.cancel();
    _currentFamilyListener?.cancel();
    _userLoginSuccessListener?.cancel();
    _deviceListChangeListener?.cancel();
    _userLogoutListener?.cancel();
    _networkListener?.cancel();
    _pushMessage?.cancel();
    _familyDetailListener?.cancel();
    _roomListChangeListener?.cancel();
    _backTopListener?.cancel();
  }

  // 滚动监听并处理UIMessage发送
  void handleBackTopButtonVisibility(double offset) {
    final bool shouldShowButton = offset > _showRocketHeight;
    if (shouldShowButton != _showBackTopBtn) {
      _showBackTopBtn = shouldShowButton;
      UIMessage.fireEvent(ShowBackTopMessage(
          tabIndex: _smartHomeIndex, showBackTop: _showBackTopBtn));
    }
  }

  void sendHideBackTopMessage() {
    if (_showBackTopBtn) {
      _showBackTopBtn = false;
      UIMessage.fireEvent(
          ShowBackTopMessage(tabIndex: _smartHomeIndex, showBackTop: false));
    }
  }
}
