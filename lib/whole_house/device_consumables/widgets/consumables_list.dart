/*
 * 描述：耗材弹窗列表内容
 * 作者：fancunshuo
 * 建立时间: 2025/3/24
 */

import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/common_network_image_widget.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/component/widget/circular_percentage_component.dart';
import 'package:smart_home/device/device_widget/device_card.dart';
import 'package:smart_home/whole_house/device_consumables/models/device_consumable_item_model.dart';
import 'package:smart_home/whole_house/device_consumables/models/device_consumables_list_view_model.dart';
import 'package:smart_home/whole_house/device_consumables/store/selectors.dart';
import 'package:smart_home/whole_house/device_consumables/utils.dart';
import 'package:smart_home/whole_house/widgets/whole_house_popup_empty.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import '../../../store/smart_home_state.dart';

const String pageName = '${SmartHomeConstant.package}/whole_house_detail_page';

class ConsumablesList extends StatelessWidget {
  const ConsumablesList({super.key, required this.showFloor});

  final bool showFloor;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, DeviceConsumablesListViewModel>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return DeviceConsumablesSelectors.selectListConsumablesViewModel(
              store.state);
        },
        builder:
            (BuildContext context, DeviceConsumablesListViewModel viewModel) {
          if (viewModel.list.isEmpty) {
            return const WholeHousePopupEmptyWidget(
              type: EmptyType.consumables,
              desc: '暂无耗材提醒',
            );
          }
          return Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: viewModel.list
                  .map((DeviceConsumableItemModel e) =>
                      _buildDeviceItem(e, context))
                  .toList(),
            ),
          );
        });
  }

  Widget _buildCommonItemLayout(
      {required double height,
      required Widget leading,
      required Widget title,
      required Widget subTitle,
      required List<Widget> actions,
      bool enable = true}) {
    return Container(
      alignment: Alignment.center,
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      color: Colors.transparent,
      child: Row(
        children: <Widget>[
          leading,
          const SizedBox(
            width: 12,
          ),
          Expanded(
            child: Opacity(
              opacity: enable ? 1 : 0.39,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  title,
                  const SizedBox(
                    height: 4,
                  ),
                  subTitle,
                ],
              ),
            ),
          ),
          const SizedBox(
            width: 12,
          ),
          Row(
            children: <Widget>[
              ...actions,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceInfo(DeviceConsumableItemModel itemModel) {
    return _buildCommonItemLayout(
        height: 75,
        leading: SizedBox(
          width: 40,
          height: 40,
          child: AspectRatio(
              aspectRatio: 1,
              child: CommonNetworkRefreshImg(
                imageUrl:
                    getSmallCardImgModel(itemModel.deviceImage).deviceIcon,
                alignment: Alignment.center,
                errorWidget: Image.asset(
                  'assets/icons/default_device_img.webp',
                  package: 'smart_home',
                  height: double.infinity,
                ),
              )),
        ),
        enable: itemModel.online,
        title: Text(
          itemModel.deviceName,
          style: TextStyle(
            color: const Color(0xFF111111),
            fontSize: 16,
            fontWeight: FontWeight.w500,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        subTitle: Text(
          '${showFloor ? itemModel.floorName : ''}${itemModel.roomName} | ${itemModel.offlineInfo}',
          style: TextStyle(
            color: const Color(0xFF666666),
            fontSize: 12,
            fontWeight: FontWeight.w400,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        actions: <Widget>[
          const SizedBox(),
        ]);
  }

  int _parsePercentage(String percentageStr) {
    return double.tryParse(percentageStr.replaceAll('%', ''))?.round() ?? 0;
  }

  Widget _buildConsumableAlarm({
    required ConsumableModel consumableModel,
    required bool highPriority,
    required BuildContext context,
  }) {
    final String surplusRatio = consumableModel.surplusRatio;
    final String saleUrl = consumableModel.saleUrl;

    final bool showPercentageCircle = surplusRatio.isNotEmpty;
    final int roundedPercentage = _parsePercentage(surplusRatio);
    final String desc = showPercentageCircle ? '剩余$roundedPercentage%' : '不足';
    final bool showBuyBtn = consumableModel.saleUrl.isNotEmpty;
    return _buildCommonItemLayout(
        height: 64,
        leading: showPercentageCircle
            ? _buildPercentageCircle(
                surplusRatio: surplusRatio, highPriority: highPriority)
            : Container(
                alignment: Alignment.center,
                width: 40,
                height: 40,
                child: Image.asset(
                  'assets/icons/consumables_info.webp',
                  width: 24,
                  height: 24,
                  package: SmartHomeConstant.package,
                ),
              ),
        title: Text(
          consumableModel.name,
          style: TextStyle(
            color: const Color(0xFF111111),
            fontSize: 14,
            fontWeight: FontWeight.w400,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        subTitle: Text(
          desc,
          style: TextStyle(
            color: highPriority
                ? AppSemanticColors.item.warn.primary
                : AppSemanticColors.item.remind.primary,
            fontSize: 11,
            fontWeight: FontWeight.w400,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        actions: <Widget>[
          if (showBuyBtn)
            _buildBuyButton(saleUrl, consumableModel.saleButtonName, context)
          else
            Image.asset(
              'assets/icons/more_arrow_grey.webp',
              width: 16,
              height: 16,
              package: SmartHomeConstant.package,
            ),
        ]);
  }

  Widget _buildPercentageCircle(
      {required String surplusRatio, bool highPriority = false}) {
    final int percentage = _parsePercentage(surplusRatio);
    return Stack(
      children: <Widget>[
        CustomPaint(
          size: const Size(40, 40),
          painter: CircularProgressPainter(
            percentage: percentage,
            lineWidth: 2,
            progressColor: highPriority
                ? AppSemanticColors.item.warn.primary
                : AppSemanticColors.item.remind.primary,
            backgroundColor: const Color(0xfff5f5f5),
          ),
        ),
        Positioned.fill(
            child: Container(
          alignment: Alignment.center,
          child: Text.rich(
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            TextSpan(children: <InlineSpan>[
              TextSpan(
                text: '$percentage',
                style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontFamilyFallback: fontFamilyFallback(),
                    fontSize: 14,
                    color: highPriority
                        ? AppSemanticColors.item.warn.primary
                        : AppSemanticColors.item.remind.primary),
              ),
              TextSpan(
                text: '%',
                style: TextStyle(
                  fontSize: 6,
                  color: highPriority
                      ? AppSemanticColors.item.warn.primary
                      : AppSemanticColors.item.remind.primary,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              )
            ]),
          ),
        ))
      ],
    );
  }

  Widget _buildBuyButton(
      String saleUrl, String saleButtonName, BuildContext context) {
    return GestureDetector(
      onTap: () {
        gioTrack(GioConst.wholeHouseClickConsumableListButton,
            <String, String>{'value': '购买'});
        goToPageWithDebounce(saleUrl);
        Navigator.of(context).pop();
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(16)),
            color: AppSemanticColors.component.information.fill),
        height: 28,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        alignment: Alignment.center,
        child: Text(
          saleButtonName,
          style: TextStyle(
            color: AppSemanticColors.component.information.on,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
      ),
    );
  }

  Widget _buildToDetailPage(DeviceConsumableItemModel itemModel,
      ConsumableModel e, BuildContext context) {
    return GestureDetector(
        onTap: () {
          gioTrack(GioConst.wholeHouseClickConsumableDetail,
              <String, String>{'value': e.name});
          Navigator.of(context).pop();
          if (itemModel.appTypeName == '净水机') {
            String url = e.detailsUrl;
            if (url.isEmpty) {
              url = SmartHomeConstant.vdnDeviceDetail + itemModel.deviceId;
            }
            goToPageWithDebounce(url);
            return;
          }
          if (e.detailsUrl.isNotEmpty) {
            goToPageWithDebounce(e.detailsUrl);
            return;
          }
          final String consumableDetailPage =
              '${SmartHomeConstant.consumableDetailPage}?deviceId=${itemModel.deviceId}&consumableCode=${e.consumableCode}';
          DevLogger.debug(
              tag: 'SmartHome',
              msg: ' consumableDetailPage url: $consumableDetailPage');
          goToPageWithDebounce(consumableDetailPage);
        },
        child: _buildConsumableAlarm(
          consumableModel: e,
          highPriority: isHighPriorityWarn(itemModel.appTypeName),
          context: context,
        ));
  }

  Widget _buildDeviceItem(
      DeviceConsumableItemModel itemModel, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        child: ColoredBox(
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              _buildDeviceInfo(itemModel),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16.0),
                color: const Color(0xFFEEEEEE),
                height: 0.5,
              ),
              Column(
                children: itemModel.consumables.values
                    .toList()
                    .map(
                      (ConsumableModel e) => PressableOverlayWidget(
                          child: _buildToDetailPage(itemModel, e, context)),
                    )
                    .toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
