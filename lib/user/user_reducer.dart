import 'package:redux/redux.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/navigator/family/family_const.dart';

import '../../store/smart_home_state.dart';
import 'user_action.dart';

final Reducer<SmartHomeState> userCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateLoginStatusAction>(_updateLoginStatus)
      .call,
  TypedReducer<SmartHomeState, LogoutAction>(_logout).call,
]);

SmartHomeState _updateLoginStatus(
    SmartHomeState state, UpdateLoginStatusAction action) {
  state.isLogin = action.isLogin;
  return state;
}

SmartHomeState _logout(SmartHomeState state, LogoutAction action) {
  state.familyState.familyName = FamilyConst.familyNameNoLogin;
  state.familyState.familyId = '';
  state.isLogin = false;
  return state;
}
