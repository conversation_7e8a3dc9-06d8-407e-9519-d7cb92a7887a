import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/component_constant.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../../common/constant.dart';
import '../../../common/smart_home_util.dart';

class UnLoginAddWidget extends StatelessWidget {
  const UnLoginAddWidget({super.key});

  static const String iconPath = 'assets/icons/unlogin_add_center_img.webp';
  static const String addText = '添加设备';
  static const String noDeviceText = '房间内无设备';

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, _AddCardModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) => _AddCardModel(
          store.state.isLogin,
          !store.state.isLogin ||
              (store.state.isLogin &&
                  store.state.familyState.familyRole != FamilyRole.member)),
      builder: (BuildContext context, _AddCardModel model) {
        /// 未登录 || （已登录 && 非成员）
        if (model.showOwnerWidget) {
          return _familyOwnerWidget(model.isLogin);
        }
        return _familyMemberWidget();
      },
    );
  }

  Widget _familyOwnerWidget(bool isLogin) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        gioTrack(SmartHomeConstant.addDeviceCardClickGio);
        if (isLogin) {
          goToPageWithDebounce(SmartHomeConstant.vdnBindPage);
        } else {
          goToPageWithDebounce(SmartHomeConstant.loginUrl);
        }
      },
      child: Container(
        height: 268,
        margin: const EdgeInsets.only(left: 16, right: 16, top: 12),
        decoration: BoxDecoration(
          borderRadius:
              const BorderRadius.all(Radius.circular(ComponentRadius.device)),
          color: AppSemanticColors.background.primary,
        ),
        child: Column(
          children: <Widget>[
            Expanded(
              child: Center(
                child: _centerImage(),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 24),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(
                      Radius.circular(ComponentRadius.componentLarge)),
                  color: AppSemanticColors.component.information.fill,
                ),
                width: double.infinity,
                height: 44,
                alignment: Alignment.center,
                child: SmartHomeText(
                  text: addText,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.component.information.on,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _familyMemberWidget() {
    return Container(
      height: 268,
      margin: const EdgeInsets.only(left: 16, right: 16, top: 12),
      decoration: BoxDecoration(
        borderRadius:
            const BorderRadius.all(Radius.circular(ComponentRadius.device)),
        color: AppSemanticColors.background.primary,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            _centerImage(),
            const SizedBox(
              height: 13,
            ),
            SmartHomeText(
                text: noDeviceText,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppSemanticColors.item.secWeaken),
          ],
        ),
      ),
    );
  }

  Widget _centerImage() {
    return Image.asset(
      iconPath,
      height: 112,
      width: 224,
      fit: BoxFit.contain,
      package: SmartHomeConstant.package,
    );
  }
}

class _AddCardModel {
  final bool isLogin;
  final bool showOwnerWidget;

  _AddCardModel(this.isLogin, this.showOwnerWidget);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _AddCardModel &&
          runtimeType == other.runtimeType &&
          isLogin == other.isLogin &&
          showOwnerWidget == other.showOwnerWidget;

  @override
  int get hashCode => isLogin.hashCode ^ showOwnerWidget.hashCode;
}
