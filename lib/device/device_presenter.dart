import 'package:device_utils/log/log.dart';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/e_heat_time_on_off_presenter.dart';
import 'package:smart_home/device/purified_consumable_presenter.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/device/store/device_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:user/user.dart';
import 'package:wash_device_manager/wash_device_manager.dart';

import '../common/constant_gio.dart';
import '../response_time_tracker/response_time_tracker.dart';
import '../time_cost_analysis/analysis_presenter.dart';
import 'device_info_model/smart_home_device_basic_info.dart';

enum DeviceListFetchTriggerType {
  pullToRefresh,
  currentFamilyChanged,
  deviceListChanged,
  roomListOrderChanged,
  deviceListEditCompleted,
  unknown,
}

class DeviceListRequestModel {
  DeviceListRequestModel({
    required this.familyId,
    this.traceId = -1,
    this.traceType = TraceType.devListChange,
    this.triggerType = DeviceListFetchTriggerType.unknown,
  });

  final String familyId;
  final int traceId;
  final TraceType traceType;
  final DeviceListFetchTriggerType triggerType;
}

class DevicePresenter {
  final List<SmartHomeDevice> washDeviceList = <SmartHomeDevice>[];
  final List<SmartHomeDevice> eHeatAndGasDeviceList = <SmartHomeDevice>[];

  /// 厨下净水器
  final List<SmartHomeDevice> purifiedWaterDeviceList = <SmartHomeDevice>[];

  static DevicePresenter? _instance;
  factory DevicePresenter.getInstance() => _getInstance();

  DevicePresenter._internal() {
    WashDeviceManager.getInstance().addListener('smart_home',
        (Set<String> basicInfo, Set<String> userPrefer) {
      smartHomeStore.dispatch(WashDeviceDataLoadFinish(basicInfo, userPrefer));
    });
  }

  static DevicePresenter _getInstance() {
    _instance ??= DevicePresenter._internal();
    return _instance!;
  }

  Future<void> getDeviceList(DeviceListRequestModel requestModel) async {
    final String curFamilyId = requestModel.familyId;
    final int traceId = requestModel.traceId;
    final TraceType traceType = requestModel.traceType;
    final DeviceListFetchTriggerType triggerType = requestModel.triggerType;

    try {
      final Map<String, DeviceInfoModel>? _originalDeviceInfoMap =
          await UpDevicePlugin.getDeviceInfoByFamilyId(curFamilyId);
      TimeConsumeStatisticTracker.trace(
          traceId: traceId, loc: TraceLocation.t2, traceType: traceType);
      if (_originalDeviceInfoMap == null) {
        smartHomeStore.dispatch(UpdateDeviceStatusAction(DeviceStatus.unknow));
        return;
      }
      DevLogger.info(
          tag: 'DevicePresenter',
          msg:
              'getDeviceInfoByFamilyId: allKeys: ${_originalDeviceInfoMap.keys}');
      smartHomeStore.dispatch(DeviceInfoMapUpdatedAction(
        originalDeviceInfoMap: _originalDeviceInfoMap,
        familyId: curFamilyId,
        traceInfo: TraceInfo(traceId, traceType),
        triggerType: triggerType,
      ));

      gioTrack(DevListGioEvent.t1GetDeviceInfoMap, <String, dynamic>{
        DevListGioEvent.sizeAllDevList: _originalDeviceInfoMap.length,
        DevListGioEvent.familyId: curFamilyId,
        DevListGioEvent.traceType: traceType.name,
      });
    } catch (error) {
      DevLogger.error(
          tag: 'DevicePresenter', msg: '__seq__ getDeviceList error:$error');
    }
  }

  void subscribeDeviceAttribute() {
    UpDevicePlugin.subscribeDeviceChangeByFamilyId(
        smartHomeStore.state.familyState.familyId,
        (Map<String, DeviceAttributeModel> deviceAttributeModelMap) {
      smartHomeStore
          .dispatch(UpdateDeviceAttributeAction(deviceAttributeModelMap));
      ResponseTimeTracker.instance
          .updateReportStateModel(deviceAttributeModelMap);
    }).catchError((dynamic error) {
      DevLogger.error(
          tag: 'DevicePresenter',
          msg: 'subscribeDeviceChangeByFamilyId error:$error');
    });
  }

  void updateTimeOnOff() {
    for (final SmartHomeDevice smartHomeDevice in eHeatAndGasDeviceList) {
      EHeatTimeOnOffPresenter.getInstance()
          .updateTimeOnOffStatus(smartHomeDevice,
              (String deviceId, SmartHomeDeviceAttribute attribute) {
        smartHomeStore
            .dispatch(UpdateCustomAttributeAction(deviceId, attribute));
      });
    }
  }

  /// 更新厨下净水机耗材信息-名称，等级
  void updatePurifiedConsumableNameAndLevel() {
    for (final SmartHomeDevice smartHomeDevice in purifiedWaterDeviceList) {
      final SmartHomeDeviceBasicInfo basicInfo = smartHomeDevice.basicInfo;
      PurifiedConsumablePresenter.getInstance()
          .updateConsumables(basicInfo.prodNo, (List<String> prodNoList) {
        smartHomeStore.dispatch(PurifiedConsumeLoadFinish(prodNoList));
      });
    }
  }

  void updateWashDeviceData() {
    for (final SmartHomeDevice smartHomeDevice in washDeviceList) {
      WashDeviceManager.getInstance()
          .loadWashDeviceBasicInfo(smartHomeDevice.basicInfo.typeId);
      WashDeviceManager.getInstance().loadWashDeviceUserPrefer(
          smartHomeDevice.basicInfo.typeId,
          User.getOauthDataSync()?.uhome_user_id ?? '',
          smartHomeDevice.basicInfo.deviceId,
          smartHomeDevice.basicInfo.ownerId);
    }
  }

  void unSubscribeDeviceAttribute() {
    UpDevicePlugin.unsubscribeDeviceChangeByFamilyId(
            smartHomeStore.state.familyState.familyId)
        .then((_) {})
        .catchError((dynamic error) {
      DevLogger.error(
          tag: 'DevicePresenter',
          msg: 'unsubscribeDeviceChangeByFamilyId error:$error');
    });
  }

  void clearDeviceList() {
    smartHomeStore.dispatch(ClearDeviceListAction());
  }

  int getDefaultTab(String tabString) {
    try {
      if (tabString == '-1') {
        return 1;
      } else {
        return int.tryParse(tabString) ?? 1;
      }
    } catch (e) {
      DevLogger.error(tag: 'DevicePresenter', msg: '_getDefaultTab:$e');
      return 1;
    }
  }
}

class SortListModel {
  SortListModel(
      {required this.cameraSortList,
      required this.wholeHouseSortList,
      required this.deviceBigCardSortList,
      required this.sortDeviceList,
      required this.sceneSortList,
      required this.timestamp});

  List<String> cameraSortList = <String>[];
  List<String> wholeHouseSortList = <String>[];
  List<String> deviceBigCardSortList = <String>[];
  List<String> sortDeviceList = <String>[];
  List<String> sceneSortList = <String>[];
  String timestamp = '';

  SortListModel.fromJson(Map<dynamic, dynamic> json) {
    timestamp = json.stringValueForKey('timestamp', '');
    final List<dynamic> cameraList =
        json.listValueForKey('cameraSortList', <dynamic>[]);
    for (final dynamic element in cameraList) {
      if (element is String) {
        cameraSortList.add(element);
      }
    }

    final List<dynamic> wholeHouseList =
        json.listValueForKey('wholeHouseSortList', <dynamic>[]);
    for (final dynamic element in wholeHouseList) {
      if (element is String) {
        wholeHouseSortList.add(element);
      }
    }

    final List<dynamic> deviceBigCardList =
        json.listValueForKey('deviceBigCardSortList', <dynamic>[]);
    for (final dynamic element in deviceBigCardList) {
      if (element is String) {
        deviceBigCardSortList.add(element);
      }
    }

    final List<dynamic> deviceList =
        json.listValueForKey('sortDeviceList', <dynamic>[]);
    for (final dynamic element in deviceList) {
      if (element is String) {
        sortDeviceList.add(element);
      }
    }

    final List<dynamic> sceneList =
        json.listValueForKey('sceneSortList', <dynamic>[]);
    for (final dynamic element in sceneList) {
      if (element is String) {
        sceneSortList.add(element);
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['timestamp'] = timestamp;
    data['cameraSortList'] = cameraSortList;
    data['wholeHouseSortList'] = wholeHouseSortList;
    data['deviceBigCardSortList'] = deviceBigCardSortList;
    data['sortDeviceList'] = sortDeviceList;
    data['sceneSortList'] = sceneSortList;
    return data;
  }

  @override
  String toString() {
    return 'SortListModel{timestamp: $timestamp, cameraSortList: $cameraSortList, wholeHouseSortList: $wholeHouseSortList, deviceBigCardSortList: $deviceBigCardSortList,sortDeviceList: $sortDeviceList, sceneSortList: $sceneSortList }';
  }
}
