import 'dart:math';

import 'package:connectivity/connectivity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';

import '../../widget_common/ui_components.dart';

/// 仪表盘顶部的小图标
class WholeHouseHeaderIconWidget extends StatelessWidget {
  const WholeHouseHeaderIconWidget(
      {super.key,
      this.icon,
      this.smallIcon = '',
      this.value = '',
      this.unit = '',
      this.count = 0,
      this.margin,
      this.clickCallback});

  /// 图标，如故障、耗材、运行时的图标，运行中含有动效，这里改为widget
  final Widget? icon;

  /// 小图标，如温度值、湿度值的图标, 和icon二选一
  final String smallIcon;

  /// 图标右边显示的数值，如温度值、湿度值等
  final String value;

  /// 数值的单位（如°C等）
  final String unit;

  /// 设备个数(故障、耗材)
  final int count;

  /// 外边距
  final EdgeInsetsGeometry? margin;

  /// 点击的回调函数
  final void Function(BuildContext context)? clickCallback;

  @override
  Widget build(BuildContext context) {
    return Stack(
      // 可以超出父容器的部分
      clipBehavior: Clip.none,
      children: <Widget>[
        GestureDetector(
          onTap: clickCallback == null ? null : () => _handleTap(context),
          child: Container(
              alignment: Alignment.center,
              width: 64,
              height: 44,
              margin: margin,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: const Color.fromRGBO(255, 255, 255, 0.50),
                border: CommonUIComponents.gradientBorder,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: <Widget>[
                  // 图标
                  IconWidget(icon: icon, smallIcon: smallIcon),
                  // 数值和单位
                  if (value.isNotEmpty)
                    ValueUnitWidget(value: value, unit: unit),
                ],
              )),
        ),
        if (count > 1)
          Positioned(
              right: 0,
              top: 0,
              child: BadgeWidget(
                count: count,
              ))
      ],
    );
  }

  // 在类定义内部添加此方法
  void _handleTap(BuildContext context) {
    Connectivity()
        .checkConnectivity()
        .then((ConnectivityResult connectivityResult) {
      if (connectivityResult == ConnectivityResult.none) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
      } else {
        clickCallback!(context);
      }
    });
  }
}

/// 图标组件
class IconWidget extends StatelessWidget {
  const IconWidget({
    super.key,
    this.icon,
    required this.smallIcon,
  });

  final Widget? icon;
  final String smallIcon;

  @override
  Widget build(BuildContext context) {
    if (icon != null) {
      return icon!;
    } else if (smallIcon.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.only(right: 2),
        child: Image.asset(
          smallIcon,
          width: 16,
          height: 16,
          package: SmartHomeConstant.package,
        ),
      );
    }
    return const SizedBox.shrink();
  }
}

/// 值和单位组件
class ValueUnitWidget extends StatelessWidget {
  const ValueUnitWidget({
    super.key,
    required this.value,
    required this.unit,
  });

  final String value;
  final String unit;

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        children: <InlineSpan>[
          TextSpan(
            text: value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xFF111111),
            ),
          ),
          if (unit.isNotEmpty) const WidgetSpan(child: SizedBox(width: 2)),
          if (unit.isNotEmpty)
            TextSpan(
              text: unit,
              style: const TextStyle(
                fontSize: 6,
                fontWeight: FontWeight.w500,
                color: Color(0xFF111111),
              ),
            ),
        ],
      ),
    );
  }
}

/// 角标组件 最少2条，最多99条，超出则显示...
class BadgeWidget extends StatelessWidget {
  const BadgeWidget({super.key, required this.count});

  final int count;

  @override
  Widget build(BuildContext context) {
    // 处理数字显示逻辑
    final String displayText = count <= 99 ? count.toString() : '···';
    return Container(
        alignment: Alignment.center,
        width: 20,
        height: 20,
        decoration: BoxDecoration(
            color: AppSemanticColors.container.card.withOpacity(0.8),
            boxShadow: const <BoxShadow>[
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, .02),
                offset: Offset(0, 4),
                blurRadius: 4,
              )
            ],
            borderRadius: BorderRadius.circular(100)),
        child: Text(displayText,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: AppSemanticColors.item.primary,
            )));
  }
}
