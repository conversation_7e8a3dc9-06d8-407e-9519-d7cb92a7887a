import 'dart:core';

import 'package:connectivity/connectivity.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_card_model.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant_gio_scene.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/device_list_widget.dart';
import 'package:smart_home/device/resize_device_card/resize_overlay.dart';
import 'package:smart_home/edit/edit_card_base_info_model.dart';
import 'package:smart_home/edit/edit_presenter/rename_presenter.dart';
import 'package:smart_home/edit/util/edit_delete_dialog.dart';
import 'package:smart_home/scene/scene_model/scene_setting_server_model.dart';
import 'package:smart_home/scene/scene_model/scene_sort_server_model.dart';
import 'package:smart_home/scene/scene_presenter.dart';
import 'package:smart_home/scene/scene_util.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/scene/store/scene_action.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:upservice/model/uhome_response_model.dart';

import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import '../../device/aggregation/agg_store/aggregation_action.dart';
import '../../device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import '../../device/aggregation/aggregation_detail/utils/aggregation_presenter.dart';
import '../../device/aggregation/aggregation_detail/widgets/aggregation_detail_list.dart';
import '../../device/aggregation/aggregation_detail/widgets/aggregation_manage_list.dart';
import '../../device/aggregation/aggregation_setting/util/aggregation_setting_manager.dart';
import '../../device/aggregation/utils/agg_utils.dart';
import '../../device/device_presenter.dart';
import '../../device/device_view_model/card_base_view_model.dart';
import '../../device/device_view_model/device_card_view_model.dart';
import '../../device/resize_device_card/resize_base_model.dart';
import '../../device/store/device_action.dart';
import '../../store/smart_home_store.dart';
import '../store/edit_action.dart';
import '../util/edit_common_dialog.dart';
import '../util/edit_constant.dart';
import '../util/edit_manager.dart';
import 'change_device_family_presenter.dart';
import 'change_device_position_presenter.dart';
import 'delete_device_presenter.dart';

/// 编辑模式下的各个页面标识
enum EditPageType {
  home,
  light,
  curtain,
  env,
  nonNet,
  camera,
  offline,
}

class EditPresenterManager {
  static Dialogs dialogs = Dialogs();

  static EditPageType currentPageForEdit = EditPageType.home;

  static void changePageWithHomeAndAgg(EditPageType pageType) {
    EditPresenterManager.currentPageForEdit = pageType;
    if (smartHomeStore.state.isEditState) {
      if (pageType == EditPageType.home) {
        gioTrack(GioConst.gioEditBack);
      }
      smartHomeStore.dispatch(UpdateEditBtnListAction(
        pageType: pageType,
      ));
    }
  }

  // 改变大小
  static void onClickChangeSize(DeviceCardType type) {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickChangeSize start');

    final List<String> _selectedIdList = smartHomeStore
        .state.editState.selectedCardsList
        .map((EditDeviceCardInfo element) => element.id)
        .toList();

    // 卡片vm修改卡片尺寸
    _selectedIdList.forEach((String id) {
      final CardBaseViewModel? viewModel = getCardVm(id);
      if (viewModel is DeviceCardViewModel) {
        viewModel.deviceCardType = type;
      }
    });

    final CardTypeForParams _cardList = assembleCardType(
        smartHomeStore.state.deviceState.allSmallCardSortIdList);
    onClickDone(
      cardTypeForParams: _cardList,
    );
  }

  // 移到顶部
  static void onClickMoveToTop() {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickMoveToTop start');
    final List<String> _moveTopIdWithSort = <String>[]; // 需要被移到顶部的设备
    final List<String> _cardOrderList = List<String>.from(
        smartHomeStore.state.deviceState.allSmallCardSortIdList);
    smartHomeStore.state.editState.selectedCardsList
        .forEach((EditDeviceCardInfo element) {
      _cardOrderList.remove(element.id);
    });
    smartHomeStore.state.deviceState.allSmallCardSortIdList
        .forEach((String id) {
      final CardBaseViewModel? viewModel = getCardVm(id);
      if (viewModel is DeviceCardViewModel && viewModel.isSelected) {
        _moveTopIdWithSort.add(id);
      }
    });
    _cardOrderList.insertAll(0, _moveTopIdWithSort);

    final CardTypeForParams _cardList = assembleCardType(_cardOrderList);
    onClickDone(
      cardTypeForParams: _cardList,
      cardOrderList: _cardOrderList,
      saveHomeCardCallback: ({bool? isSuccess}) {
        if (isSuccess ?? false) {
          smartHomeStore.dispatch(SmallCardDragFinishedAction(_cardOrderList));
        }
      },
    );
  }

  // 点击取消或左滑退出
  static void onCancel(BuildContext context) {
    // 移除拖动调整大小图层
    removeOverlay();
    gioTrack(GioConst.gioCancel);

    // 回滚场景列表
    smartHomeStore.dispatch(RollbackSceneListAction());

    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onCancel '
            'isChangeSizeOrSort = ${isChangeSizeOrSort()}');
    if (isChangeSizeOrSort()) {
      smartHomeStore.dispatch(ExitEditStateAction());
      updateDeviceListAfterEdit();
    } else {
      smartHomeStore.dispatch(ExitEditStateAction());
    }
  }

  // 刷新设备列表
  static void updateDeviceListAfterEdit() {
    final int traceId = DateTime.now().millisecondsSinceEpoch;
    final FamilyModel? curFamily = Family.getCurrentFamilySync();
    final String curFamilyId =
        curFamily?.familyId ?? smartHomeStore.state.familyState.familyId;
    DevicePresenter.getInstance().getDeviceList(DeviceListRequestModel(
      familyId: curFamilyId,
      traceId: traceId,
      triggerType: DeviceListFetchTriggerType.deviceListEditCompleted,
    ));
  }

  // 判断卡片尺寸或排序是否发生变化
  static bool isChangeSizeOrSort() {
    bool reOrder = false;

    // 标记需要执行保存逻辑的直接返回true
    if (smartHomeStore.state.editState.isNeedSaveEditedCardListData) {
      return true;
    }

    for (int i = 0;
        i < smartHomeStore.state.editState.cacheCardList.length;
        i++) {
      final String vmId =
          smartHomeStore.state.deviceState.allSmallCardSortIdList[i];
      final CardBaseViewModel? viewModel = getCardVm(vmId);
      final CacheCardModel cacheVm =
          smartHomeStore.state.editState.cacheCardList[i];
      if (viewModel is DeviceCardViewModel) {
        if (vmId != cacheVm.id || viewModel.deviceCardType != cacheVm.type) {
          reOrder = true;
          break;
        }
      }
    }
    return reOrder;
  }

  static bool get isWholeHouseTabSelected {
    return smartHomeStore.state.deviceState.selectedRoomId ==
        smartHomeStore.state.familyState.familyId;
  }

  static List<SceneItemViewModel> get currentRoomSceneList =>
      smartHomeStore.state.sceneState
          .sceneMap[smartHomeStore.state.deviceState.selectedRoomId] ??
      <SceneItemViewModel>[];

  static void onClickRename(BuildContext context) {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickRename start');
    RenamePresenter.showDialog(context);
  }

  static void onClickChangeDevicePosition(BuildContext context) {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickChangeDevicePosition start');
    ChangeDevicePositionPresenter.showDialog(context);
  }

  static void onClickShare() {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickShare start');
    final List<EditDeviceCardInfo> selectedCardsList =
        smartHomeStore.state.editState.selectedCardsList;

    if (selectedCardsList.isEmpty) {
      DevLogger.info(
          tag: SmartHomeEditManager.tag,
          msg: 'EditPresenterManager onClickShare selectedCardsList.isEmpty');
      return;
    }
    gioTrack(GioConst.shareClick, <String, dynamic>{
      'model_type': '设备共享',
      'size_all_devList': selectedCardsList.length
    });
    final String _deviceSharingPath =
        _buildDeviceSharingPath(selectedCardsList);
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg:
            'EditPresenterManager onClickShare deviceSharingPath = $_deviceSharingPath');
    onClickDone();
    goToPage(_deviceSharingPath);
  }

  static String _buildDeviceSharingPath(List<EditDeviceCardInfo> cards) {
    final List<String> deviceIdList = <String>[];
    final List<String> deviceRoleList = <String>[];
    final List<String> productCodeList = <String>[];

    for (final EditDeviceCardInfo card in cards) {
      deviceIdList.add(card.id);
      deviceRoleList.add(card.deviceRole);
      productCodeList.add(card.productCode);
    }

    final String deviceIds = deviceIdList.join('|');
    final String deviceRoles = deviceRoleList.join('|');
    final String productCodes = productCodeList.join('|');

    final EditDeviceCardInfo firstCard = cards[0];
    final String deviceIcon = Uri.encodeComponent(firstCard.deviceIcon);
    final String deviceName = Uri.encodeComponent(firstCard.deviceName);

    String path = 'mpaas://deviceSharing?'
        'deviceIds=$deviceIds&'
        'deviceName=$deviceName&'
        'deviceImg=$deviceIcon&'
        'deviceRoles=$deviceRoles&'
        'productCodes=$productCodes#/';

    path += cards.length > 1 ? 'multiDevice' : '';

    return path;
  }

  static void onClickChangeDeviceFamilyPosition(BuildContext context) {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickChangeDeviceFamilyPosition start');
    ChangeDeviceFamilyPresenter.showFamilyChangeDialog(context);
  }

  static void onClickDeleteDevice(BuildContext context) {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickDeleteDevice start');
    DeleteDevicePresenter.showDialog(context);
  }

  // 关闭聚合
  static void onCLickCloseAggregation(BuildContext context) {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onCLickCloseAggregation start');
    if (smartHomeStore.state.editState.selectedCardsList.isNotEmpty) {
      // 聚合卡片ID
      final String realId =
          smartHomeStore.state.editState.selectedCardsList[0].id;

      // 打开聚合开关弹框
      dialogs.showDoubleBtnDialog(
        context: context,
        title: AggregationSettingManager.getDialogTitleBySwitchAndId(
            getAggrPrefix(realId), false),
        content: AggregationSettingManager.getDialogContentBySwitchAndId(
            getAggrPrefix(realId), false),
        confirmCallback: () {
          Connectivity().checkConnectivity().then((ConnectivityResult result) {
            if (result == ConnectivityResult.none) {
              ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
              return;
            }
            smartHomeStore.dispatch(UpdateAggregationStateAction(
                id: getAggrPrefix(realId), isSelected: false));
            SmartHomeEditManager.closeEditDialog();

            final List<String> _cardOrderList = List<String>.from(
                smartHomeStore.state.deviceState.allSmallCardSortIdList);
            _cardOrderList.remove(realId);

            final CardTypeForParams _cardList =
                assembleCardType(_cardOrderList);
            EditPresenterManager.onClickDone(
              cardTypeForParams: _cardList,
              cardOrderList: _cardOrderList,
            );
          });
        },
      );
    }
  }

  // 编辑
  static void onCLickAggregationSetting(BuildContext context) {
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onCLickAggregationSetting start');
    if (smartHomeStore.state.editState.selectedCardsList.isNotEmpty) {
      final String id = smartHomeStore.state.editState.selectedCardsList[0].id;
      if (getCardVm(id) is AggregationBaseViewModel) {
        onClickDone();
        Navigator.push(
            context,
            MaterialPageRoute<AggregationDetailList>(
                builder: (BuildContext context) => AggregationManageList(
                      vm: getCardVm(id)! as AggregationBaseViewModel,
                    )));
      }
    }
  }

  // 点击移出(场景)
  static void onClickSceneRemove(BuildContext context) {
    gioTrack(GioScene.gioSceneEditRemove, gioRoomNameParam);
    final List<String> sceneIds = smartHomeStore
        .state.editState.selectedSceneCardList
        .map((EditDeviceCardInfo e) => e.id)
        .toList();
    DevLogger.debug(
        tag: SmartHomeEditManager.tag,
        msg:
            'EditPresenterManager onClickSceneRemove start, sceneIds:$sceneIds');
    dialogs.showDoubleBtnDialog(
        context: context,
        title: '确定移出选中的手动控制？',
        content: '',
        confirmText: '移出',
        confirmCallback: () {
          Connectivity().checkConnectivity().then((ConnectivityResult result) {
            if (result == ConnectivityResult.none) {
              ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
              return;
            }

            SmartHomeEditManager.closeEditDialog();

            // 请求场景移出接口 -- sceneIds
            HttpService.roomSceneBatchSetting(ManualSceneSettingRequestModel(
                    noDisplaySceneIds: sceneIds,
                    roomId: smartHomeStore.state.deviceState.selectedRoomId ==
                            smartHomeStore.state.familyState.familyId
                        ? null
                        : smartHomeStore.state.deviceState.selectedRoomId,
                    familyId: smartHomeStore.state.familyState.familyId))
                .then((UhomeResponseModel? res) {
              DevLogger.debug(
                  tag: SmartHomeEditManager.tag,
                  msg:
                      'EditPresenterManager onClickSceneRemove sceneBatchSetting end, res:$res');
              ScenePresenter()
                  .fetchSceneData(smartHomeStore.state.familyState.familyId);
            }).catchError((dynamic error) {
              DevLogger.error(
                  tag: SmartHomeEditManager.tag,
                  msg:
                      'EditPresenterManager onClickSceneRemove sceneBatchSetting error, error:$error');
            });
            onClickDone();
          });
        },
        cancelCallback: () {
          smartHomeStore.dispatch(ExitEditStateAction());
        });
  }

  // 点击编辑(场景)
  static void onClickSceneEdit() {
    gioTrack(GioScene.gioSceneEdit, gioRoomNameParam);
    final String sceneId =
        smartHomeStore.state.editState.selectedSceneCardList.length == 1
            ? smartHomeStore.state.editState.selectedSceneCardList[0].id
            : '';
    final SceneItemViewModel curSceneVm = currentRoomSceneList.firstWhere(
        (SceneItemViewModel element) => element.sceneId == sceneId,
        orElse: () => SceneItemViewModel(
              sceneId: sceneId,
              sceneName: '',
              sceneIcon: '',
            ));

    // 跳转场景H5详情页
    final String sceneDetail = _buildSceneDetailUrl(curSceneVm);

    onClickDone();
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg:
            'EditPresenterManager onClickSceneEdit start, sceneDetail:$sceneDetail');
    goToPageWithDebounce(sceneDetail);
  }

  static String _buildSceneDetailUrl(SceneItemViewModel curScene) {
    if (curScene.dataVersion == '2') {
      if (curScene.templateId == '') {
        // 2.0自定义手动场景
        return 'mpaas://scene_mi?needAuthLogin=1&familyId=${smartHomeStore.state.familyState.familyId}&sceneId=${curScene.sceneId}&channelName=智家页#scenemade/manually';
      }
      // 2.0模版场景
      return 'mpaas://scene_mi?needAuthLogin=1&familyId=${smartHomeStore.state.familyState.familyId}&sceneId=${curScene.sceneId}&channelName=智家页&templateType=${curScene.type}#templatedetail';
    }

    // 默认线上1.0场景详情页
    return 'mpaas://scene?needAuthLogin=1&sceneId=${curScene.sceneId}#scenedetail';
  }

  // 点击保存
  static Future<void> onClickDone({
    CardTypeForParams? cardTypeForParams,
    List<String>? cardOrderList,
    void Function({bool isSuccess})? saveHomeCardCallback,
    bool isNeedCloseDialog = true,
    bool isNeedExitEdit = true
  }) async {
    if (isNeedCloseDialog) {
      SmartHomeEditManager.closeEditDialog();
    }
    bool isNeedExecuteCb = true;
    final bool needSaveDeviceCards = isChangeSizeOrSort() ||
        cardTypeForParams != null ||
        cardOrderList != null ||
        saveHomeCardCallback != null;
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickDone '
            'isChangeSizeOrSort = ${isChangeSizeOrSort()}');
    if (needSaveDeviceCards) {
      isNeedExecuteCb = false;
      final CardTypeForParams _cardList = cardTypeForParams ??
          assembleCardType(
              smartHomeStore.state.deviceState.allSmallCardSortIdList);

      saveCardsIdList(
        cardTypeForParams: _cardList,
        cardOrderList: cardOrderList,
      ).then((bool success) {
        if (saveHomeCardCallback != null) {
          saveHomeCardCallback(isSuccess: success);
        }
        if (!success) {
          ToastHelper.showToast(EditConstant.saveFail);
        }
      });
    }

    if (saveHomeCardCallback != null && isNeedExecuteCb) {
      saveHomeCardCallback();
    }

    // 场景卡片排序保存 - 非全屋tab不支持排序 故无需保存
    if (isWholeHouseTabSelected) {
      _saveSceneListSort();
    }
    AggregationPresenter.saveAggSort();

    if (smartHomeStore.state.editState.isNeedSaveEditedCardListData) {
      smartHomeStore.dispatch(ResetIsNeedSaveEditedCardListDataAction());
    }
    if (isNeedExitEdit) {
      smartHomeStore.dispatch(ExitEditStateAction());
    }
  }

  static void _saveSceneListSort() {
    final List<SceneItemViewModel> oriSceneList =
        smartHomeStore.state.editState.cacheSceneCardList;

    final List<SceneSortModel> sceneSort = <SceneSortModel>[];
    for (int i = 0; i < currentRoomSceneList.length; i++) {
      final SceneItemViewModel sceneVM = currentRoomSceneList[i];
      sceneSort.add(SceneSortModel(
          sceneId: sceneVM.sceneId,
          type: sceneVM.type,
          appSort: oriSceneList[i].appSort));
    }

    final ManualSceneSortRequestModel req = ManualSceneSortRequestModel(
        familyId: smartHomeStore.state.familyState.familyId,
        sceneSort: sceneSort);
    DevLogger.debug(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager onClickDone saveSceneListSort, req:$req');
    HttpService.saveSceneListSort(req).then((UhomeResponseModel? res) {
      DevLogger.debug(
          tag: SmartHomeEditManager.tag,
          msg:
              'EditPresenterManager onClickDone saveSceneListSort end, res:$res');

      if (res?.retCode == SmartHomeConstant.zjServerRetSuccessCode) {
        ScenePresenter()
            .fetchSceneData(smartHomeStore.state.familyState.familyId);
      }
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: SmartHomeEditManager.tag,
          msg:
              'EditPresenterManager onClickDone saveSceneListSort error: $err');
    });
  }

  /// 编辑保存接口调用
  static Future<bool> saveCardsIdList({
    required CardTypeForParams cardTypeForParams,
    List<String>? cardOrderList,
  }) async {
    // 获取familyId
    final FamilyModel? familyModel = Family.getCurrentFamilySync();
    final String familyId = familyModel?.familyId ?? '';

    // 组装设备排序列表
    final List<String> _cardOrderList = cardOrderList ??
        smartHomeStore.state.deviceState.allSmallCardSortIdList;

    // 还原被聚合的设备的排序
    _cardOrderList.removeWhere((String id) => hiddenIds.keys.contains(id));
    for (final String key in hiddenIds.keys) {
      _cardOrderList.insert(hiddenIds[key]!, key);
    }

    final DeviceCardStatusModel request = DeviceCardStatusModel(
      familyId: familyId,
      bigCardList: cardTypeForParams.bigCardList,
      middleCardList: cardTypeForParams.middleCardList,
      smallCardList: cardTypeForParams.smallCardList,
      cardOrderList: _cardOrderList,
    );
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'EditPresenterManager saveCardsIdList request = $request');
    try {
      DevLogger.info(
          tag: SmartHomeEditManager.tag,
          msg: 'EditPresenterManager saveCardsIdList request = $request');
      final DeviceCardResult responseModel =
          await Family.operateDeviceCardStatus(request);
      final bool isSuccess = responseModel.retCode ==
          SmartHomeConstant.logicEngineOperateSuccessCode;
      return isSuccess;
    } catch (e) {
      DevLogger.info(
          tag: SmartHomeEditManager.tag,
          msg: 'EditPresenterManager saveCardsIdList '
              'catch error: $e');
      return false;
    }
  }

  // 获取卡片Vm
  static DeviceCardViewModel? getCardVm(String id) {
    final CardBaseViewModel? viewModel =
        smartHomeStore.state.deviceState.allCardViewModelMap[id];
    if (viewModel is DeviceCardViewModel) {
      return viewModel;
    }
    return null;
  }

  // 大中小卡片分类用于编辑保存接口请求
  static CardTypeForParams assembleCardType(List<String> cardList) {
    final CardTypeForParams cartTypeList = CardTypeForParams();
    cardList.forEach((String id) {
      final CardBaseViewModel? viewModel = getCardVm(id);
      if (viewModel is DeviceCardViewModel) {
        if (viewModel.deviceCardType == DeviceCardType.largeCard) {
          cartTypeList.bigCardList.add(id);
        }
        if (viewModel.deviceCardType == DeviceCardType.smallCard) {
          cartTypeList.smallCardList.add(id);
        }
        if (viewModel.deviceCardType == DeviceCardType.middleCard) {
          cartTypeList.middleCardList.add(id);
        }
      }
    });
    return cartTypeList;
  }
}

class CardTypeForParams {
  List<String> bigCardList = <String>[];
  List<String> middleCardList = <String>[];
  List<String> smallCardList = <String>[];
}
