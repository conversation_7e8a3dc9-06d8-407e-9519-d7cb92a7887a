import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/common_network_image_widget.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/location_weather/whole_house_header_weather_vm.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import '../../common/constant.dart';
import '../../common/smart_home_util.dart';
import '../device_env/pages/env_device_setting_page.dart';
import 'weather_selectors.dart';

/// 仪表盘顶部天气小图标
class WholeHouseHeaderWeatherWidget extends StatefulWidget {
  final String spaceId;
  final String spaceName;

  const WholeHouseHeaderWeatherWidget({
    super.key,
    required this.spaceId,
    required this.spaceName,
  });

  @override
  State<WholeHouseHeaderWeatherWidget> createState() =>
      _WholeHouseHeaderWeatherWidgetState();
}

class _WholeHouseHeaderWeatherWidgetState
    extends State<WholeHouseHeaderWeatherWidget> {
  @override
  void initState() {
    super.initState();
    
    gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
      'value': '天气',
    });
  }

  @override
  void didUpdateWidget(WholeHouseHeaderWeatherWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.spaceId != widget.spaceId) {
      gioTrack(GioConst.wholeHouseIconAppear, <String, String>{
        'value': '天气',
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用StoreConnector连接Redux与UI
    return StoreConnector<SmartHomeState, WholeHouseHeaderWeatherViewModel>(
      // 数据转换器，从Store提取ViewModel
      converter: (Store<SmartHomeState> store) =>
          WeatherSelectors.weatherViewModelWithRoomIdSelector(store.state, widget.spaceId),
      distinct: true,
      builder:
          (BuildContext context, WholeHouseHeaderWeatherViewModel viewModel) {
        if (!viewModel.visible) {
          return const SizedBox.shrink();
        }
        return _buildWeatherWidget(context, viewModel);
      },
    );
  }

  /// 构建天气组件
  Widget _buildWeatherWidget(
      BuildContext context, WholeHouseHeaderWeatherViewModel viewModel) {
    return GestureDetector(
      onTap: () {
        if (isFamilyMemberRole()) {
          ToastHelper.showToast(SmartHomeConstant.contactFamilyRoleWarning);
          return;
        }
        gioTrack(GioConst.wholeHouseIconClick, <String, String>{
          'value': '天气',
        });
        Navigator.of(context).push(
          MaterialPageRoute<void>(
            builder: (BuildContext context) => EnvDeviceSettingPage(
              spaceId: widget.spaceId,
            ),
          ),
        );
      },
      child: Container(
        constraints: const BoxConstraints(minWidth: 120),
        height: 44,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(1000),
          child: Stack(
            children: <Widget>[
              // 背景图或背景色
              Positioned.fill(
                child: viewModel.customBackgroundImage.isNotEmpty
                    ? ColorFiltered(
                        colorFilter: ColorFilter.mode(
                          Colors.blue.withOpacity(0.3),
                          BlendMode.srcOver,
                        ),
                        child: CommonNetworkRefreshImg(
                          imageUrl: viewModel.customBackgroundImage,
                          fit: BoxFit.cover,
                          alignment: Alignment.center,
                        ),
                      )
                    : Container(color: viewModel.backgroundColor),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 24),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    // 天气图标
                    Center(
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: viewModel.icon.isEmpty
                              ? Colors.white.withOpacity(0.7)
                              : null,
                        ),
                        child: viewModel.icon.isNotEmpty
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: AspectRatio(
                                    aspectRatio: 1,
                                    child: CommonNetworkRefreshImg(
                                      imageUrl: viewModel.icon,
                                      alignment: Alignment.center,
                                      errorWidget: Image.asset(
                                        'assets/icons/default_device_img.webp',
                                        package: 'smart_home',
                                        height: double.infinity,
                                      ),
                                    )))
                            : null,
                      ),
                    ),

            const SizedBox(width: 8),

            // 温度+单位
            Padding(
              padding: const EdgeInsets.only(top: 1),
              child: Text(
                '${viewModel.value}${viewModel.unit}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  height: 1,
                  fontWeight: FontWeight.w600,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              ),
            ),

            const SizedBox(width: 4),

            // 地区名称
            Flexible(
              child: Padding(
                padding: const EdgeInsets.only(top: 1),
                child: Text(
                  viewModel.area.length > 4
                      ? '${viewModel.area.substring(0, 4)}...'
                      : viewModel.area,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    height: 1,
                    fontWeight: FontWeight.w400,
                    fontFamilyFallback: fontFamilyFallback(),
            ),
                      ),
                    ),
                  ),
                ],
              ),
            )],
          ),
        ),
      ),
    );
  }
}
