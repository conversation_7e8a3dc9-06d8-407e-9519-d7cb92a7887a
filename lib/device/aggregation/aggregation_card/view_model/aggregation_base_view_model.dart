import 'dart:async';

import 'package:connectivity/connectivity.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_detail_list.dart';
import 'package:smart_home/device/component/view_model/expand_switch_icon_text_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:upsystem/upsystem.dart';

import '../../../../common/constant_gio.dart';
import '../../../../edit/edit_presenter/edit_presenter_manager.dart';
import '../../../../edit/store/edit_action.dart';
import '../../../component/view_model/expand_empty_view_model.dart';
import '../../../component_view_model/component_view_model.dart';
import '../../../device_view_model/device_card_view_model.dart';
import '../../agg_store/aggregation_action.dart';
import '../../aggregation_detail/view_model/detail_room_list.dart';
import '../../aggregation_detail/view_model/detail_room_list_item.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';
import '../../aggregation_setting/util/aggregation_setting_manager.dart';
import '../../utils/agg_utils.dart';
import '../util/aggregation_device_constant.dart';
import 'aggregation_empty_view_model.dart';
import 'aggregation_expand_grid_view_model.dart';

/// Aggregation Base View Model
abstract class AggregationBaseViewModel extends DeviceCardViewModel {
  /// 卡片类型，父类已经定义
  /// DeviceCardType deviceCardType = super.deviceCardType;
  /// 标题，在子类中复写deviceName
  /// 子标题，在子类中复写status，deviceCardAttribute，smallCardStatus
  /// 图标，在子类中复写deviceIcon
  /// 全无级别设备列表状态：未知，部分开关，全部打开，全部关闭，全部离线
  DeviceStateForAggregation deviceStateForAggregation =
      DeviceStateForAggregation.UNKNOWN;

  /// 几个房间开启
  int openRoomCount = 0;
  int onlineRoomCount = 0;

  /// 已聚合设备列表
  Map<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel> devicesByRoom =
      <SmartHomeRoomInfo, AggregationDevicesByRoomViewModel>{};

  /// 房间列表
  List<SmartHomeRoomInfo> roomList = <SmartHomeRoomInfo>[];

  bool aggregationOperating = false;

  /// 是否支持全开全关
  bool isOperate = true;

  /// 是否展示横向房间列表
  bool isShowRoomList = true;

  Timer? operatingTimer;

  Timer? operatingRoomTimer;

  // 当前选中的房间
  int currentIndex = 0;

  // 当前全开/全关的房间和状态
  bool roomOperating = false;
  String? roomIdOperating;

  DetailRoomList detailRoomsModel = DetailRoomList(<DetailRoomListItem>[]);

  void clearCache() {
    devicesByRoom.clear();
    roomList.clear();
  }

  // 管理页面标题副标题, 详情页未聚合设备房间状态。
  // 固定参数，不需要更新的字段，因此不需要比较
  String get managePageTitle => '设备管理';

  String get managePageSubTitle => '添加或删除在聚合页面展示的设备';

  String get manageDialogTitle => '添加聚合设备';

  String get manageListEmpty => '暂无设备';

  String get wholeHouseDeviceAllOpenTip => '所有设备已开启';

  String get wholeHouseDeviceAllCloseTip => '所有设备已关闭';

  /// 是否支持大卡片
  @override
  bool get supportLargeCard {
    return true;
  }

  @override
  bool get isDeviceIconAsset => true;

  @override
  bool get deviceOffline => false;

  String getStatus() {
    return '';
  }

  final Map<String, EditPageType> _deviceTypeMap = <String, EditPageType>{
    AggregationSettingConstant.agg_light_id: EditPageType.light,
    AggregationSettingConstant.agg_curtain_id: EditPageType.curtain,
    AggregationSettingConstant.env_id: EditPageType.env,
  };

  @override
  int get deviceCount => devicesByRoom.values.fold(
      0,
      (int sum, AggregationDevicesByRoomViewModel roomModel) =>
          sum + roomModel.deviceList.length);

  AggregationBaseViewModel({required super.device});

  List<ComponentBaseViewModel> getRoomComponentViewModelListOne() {
    final List<ComponentBaseViewModel> viewModelList =
        <ComponentBaseViewModel>[];
    roomList.forEach((SmartHomeRoomInfo roomInfo) {
      final AggregationDevicesByRoomViewModel? roomViewModel =
          devicesByRoom[roomInfo];
      if (roomViewModel is AggregationDevicesByRoomViewModel) {
        final bool isRoomOn = roomViewModel.isRoomOpen;
        final bool showFloor = smartHomeStore.state.deviceState.cardShowFloor;
        String floorName = '', roomName = roomInfo.roomName;
        if (showFloor) {
          floorName = roomInfo.floorName;
        }
        viewModelList.add(ExpandSwitchIconTextViewModel(
            icon: getRoomIcon(isRoomOn),
            text: '$floorName$roomName',
            enable: isRoomEnable(roomViewModel),
            isOn: isRoomOn,
            clickCallback: (BuildContext context) {
              // handle room click
              roomClickCallback(isRoomOn, roomViewModel, context);
            }));
      }
    });
    viewModelList.add(ExpandEmptyViewModel());
    return viewModelList;
  }

  List<ComponentBaseViewModel> getRoomComponentViewModelList() {
    final List<ComponentBaseViewModel> viewModelList =
        <ComponentBaseViewModel>[];
    roomList.forEach((SmartHomeRoomInfo roomInfo) {
      final AggregationDevicesByRoomViewModel? roomViewModel =
          devicesByRoom[roomInfo];
      if (roomViewModel is AggregationDevicesByRoomViewModel) {
        final bool isRoomOn = roomViewModel.isRoomOpen;
        final bool showFloor = smartHomeStore.state.deviceState.cardShowFloor;
        String floorName = '', roomName = roomInfo.roomName;
        if (showFloor) {
          floorName = roomInfo.floorName;
        }
        viewModelList.add(ExpandSwitchIconTextViewModel(
            icon: getRoomIcon(isRoomOn),
            text: '$floorName$roomName',
            enable: isRoomEnable(roomViewModel),
            isOn: isRoomOn,
            clickCallback: (BuildContext context) {
              // handle room click
              roomClickCallback(isRoomOn, roomViewModel, context);
            }));
      }
    });
    return viewModelList;
  }

  List<ComponentBaseViewModel> getRoomListViewModel() {
    final List<ComponentBaseViewModel> viewModelList =
        <AggregationExpandGridViewModel>[];
    final List<ComponentBaseViewModel> cardRoomList =
        <ExpandSwitchIconTextViewModel>[];
    roomList.forEach((SmartHomeRoomInfo roomInfo) {
      final AggregationDevicesByRoomViewModel? roomViewModel =
          devicesByRoom[roomInfo];
      if (roomViewModel is AggregationDevicesByRoomViewModel) {
        final bool isRoomOn = roomViewModel.isRoomOpen;
        final bool showFloor = smartHomeStore.state.deviceState.cardShowFloor;
        String floorName = '', roomName = roomInfo.roomName;
        if (showFloor) {
          floorName = roomInfo.floorName;
        }
        cardRoomList.add(ExpandSwitchIconTextViewModel(
            icon: getRoomIcon(isRoomOn),
            text: '$floorName$roomName',
            enable: isRoomEnable(roomViewModel),
            isOn: isRoomOn,
            clickCallback: (BuildContext context) {
              // handle room click
              roomClickCallback(isRoomOn, roomViewModel, context);
            }));
      }
    });
    // viewModelList.add(AggregationRoomListViewModel(roomList: roomList));
    viewModelList.add(AggregationExpandGridViewModel(roomList: cardRoomList));
    return viewModelList;
  }

  List<ComponentBaseViewModel> getEmptyViewModel() {
    return <ComponentBaseViewModel>[AggregationEmptyViewModel()];
  }

  void roomClickCallback(bool isRoomOn,
      AggregationDevicesByRoomViewModel roomViewModel, BuildContext context) {
    gioTrack(AggregationDeviceConstant.cardClickGio, <String, dynamic>{
      'sourceName': AggregationSettingManager.getSourceNameById(deviceId),
      'value': '房间'
    });
    // handle room click
    if (!isRoomOn) {
      roomViewModel.turnOnAllRoomDevices(context, deviceId, () {
        handleOperating();
        handleRoomOperating(roomViewModel.roomInfo?.roomId ?? '');
      });
    } else {
      roomViewModel.turnOffAllRoomDevices(context, deviceId, () {
        handleOperating();
        handleRoomOperating(roomViewModel.roomInfo?.roomId ?? '');
      });
    }
  }

  void roomClickTurnOnAllForDetail(
      AggregationDevicesByRoomViewModel roomViewModel, BuildContext context) {
    // handle room turn on all
    gioAggDetailBtnClick(
        GioConst.aggDetailBtn, deviceId, AggregationDeviceConstant.gioRoomOpen);
    roomViewModel.turnOnAllRoomDevices(context, deviceId, () {
      handleOperating();
      handleRoomOperating(roomViewModel.roomInfo?.roomId ?? '');
    });
  }

  void roomClickTurnOffAllForDetail(
      AggregationDevicesByRoomViewModel roomViewModel, BuildContext context) {
    // handle room turn off all
    gioAggDetailBtnClick(GioConst.aggDetailBtn, deviceId,
        AggregationDeviceConstant.gioRoomClose);
    roomViewModel.turnOffAllRoomDevices(context, deviceId, () {
      handleOperating();
      handleRoomOperating(roomViewModel.roomInfo?.roomId ?? '');
    });
  }

  Future<void> handleRoomOperating(String roomId) async {
    final ConnectivityResult result = await Connectivity().checkConnectivity();
    if (result == ConnectivityResult.none) {
      ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      return;
    }
    smartHomeStore
        .dispatch(UpdateAggregationRoomOperateAction(true, deviceId, roomId));
    // 先取消，再启动
    operatingRoomTimer?.cancel();
    final int durationSeconds = isDeviceCurtainAggregation(deviceId) ? 10 : 1;
    operatingRoomTimer = Timer(Duration(seconds: durationSeconds), () {
      smartHomeStore.dispatch(
          UpdateAggregationRoomOperateAction(false, deviceId, roomId));
      operatingRoomTimer?.cancel();
    });
  }

  Future<void> handleOperating() async {
    final ConnectivityResult result = await Connectivity().checkConnectivity();
    if (result == ConnectivityResult.none) {
      ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      return;
    }
    aggregationOperating = true;
    smartHomeStore
        .dispatch(UpdateAggregationCardAction(aggregationOperating, deviceId));
    // 先取消，再启动
    operatingTimer?.cancel();
    final int durationSeconds = isDeviceCurtainAggregation(deviceId) ? 10 : 1;
    operatingTimer = Timer(Duration(seconds: durationSeconds), () {
      aggregationOperating = false;
      smartHomeStore.dispatch(
          UpdateAggregationCardAction(aggregationOperating, deviceId));
      operatingTimer?.cancel();
    });
  }

  String getRoomIcon(bool isRoomOn) {
    return '';
  }

  bool isRoomEnable(AggregationDevicesByRoomViewModel viewModel) {
    return true;
  }

  /// 卡片点击事件处理
  @override
  void cardClick(BuildContext? context, {String? jumpUrl}) {
    gioTrack(AggregationDeviceConstant.cardClickGio, <String, dynamic>{
      'sourceName': AggregationSettingManager.getSourceNameById(deviceId),
      'value': '进详情页'
    });
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (ConnectivityResult.none == result) {
        // ToastHelper
        ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      } else {
        try {
          if (!smartHomeStore.state.isEditState) {
            smartHomeStore.dispatch(UpdateAggDetailListLoadingAction());
          }
          EditPresenterManager.changePageWithHomeAndAgg(_getEditPageType());
          Navigator.push(
              context!,
              MaterialPageRoute<AggregationDetailList>(
                  builder: (BuildContext context) => AggregationDetailList(
                        aggregationId: deviceId,
                      )));
        } catch (e) {
          DevLogger.error(
            tag: 'smart_home',
            msg: 'show detail page failed $e',
          );
        }
      }
    });
  }

  EditPageType _getEditPageType() {
    return _deviceTypeMap.entries
        .firstWhere(
          (MapEntry<String, EditPageType> entry) =>
              deviceId.contains(entry.key),
          orElse: () => const MapEntry<String, EditPageType>(
            AggregationSettingConstant.agg_light_id,
            EditPageType.light,
          ),
        )
        .value;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationBaseViewModel &&
          runtimeType == other.runtimeType &&
          deviceStateForAggregation == other.deviceStateForAggregation &&
          openRoomCount == other.openRoomCount &&
          mapEquals(devicesByRoom, other.devicesByRoom) &&
          aggregationOperating == other.aggregationOperating &&
          listEquals(roomList, other.roomList) &&
          roomIdOperating == other.roomIdOperating &&
          roomOperating == other.roomOperating &&
          detailRoomsModel == other.detailRoomsModel;

  @override
  int get hashCode =>
      super.hashCode ^
      deviceStateForAggregation.hashCode ^
      openRoomCount.hashCode ^
      mapHashCode(devicesByRoom) ^
      aggregationOperating.hashCode ^
      listHashCode(roomList) ^
      roomOperating.hashCode ^
      roomIdOperating.hashCode ^
      detailRoomsModel.hashCode;

  void turnOnAllDevices(BuildContext? c) {
    UpSystem.impactFeedBack();
    DevLogger.debug(tag: 'SmartHomeDevice', msg: 'turnOnAllDevices start.');
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      } else {
        if (smartHomeStore.state.isEditState) {
          return;
        }
        if (DeviceStateForAggregation.ALL_OFFLINE ==
            deviceStateForAggregation) {
          ToastHelper.showToast('相关设备已离线，请检查后再试');
        } else if (DeviceStateForAggregation.ALL_OPEN ==
            deviceStateForAggregation) {
          ToastHelper.showToast(wholeHouseDeviceAllOpenTip);
        } else if (onlineRoomCount > 0) {
          handleOperating();
          // 命令下发处理
          emitPowerCommand(true);
          debugPrint('whole house emitPowerCommand true');
        } else {
          DevLogger.debug(
              tag: 'SmartHomeDevice', msg: 'turnOnAllDevices not match.');
        }
      }
    });
  }

  @override
  void emitPowerCommand(bool powerOn) {}

  void turnOffAllDevices(BuildContext? c) {
    UpSystem.impactFeedBack();
    DevLogger.debug(tag: 'SmartHomeDevice', msg: 'turnOffAllDevices start.');
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      } else {
        if (smartHomeStore.state.isEditState) {
          return;
        } else if (DeviceStateForAggregation.ALL_OFFLINE ==
            deviceStateForAggregation) {
          ToastHelper.showToast('相关设备已离线，请检查后再试');
        } else if (DeviceStateForAggregation.ALL_CLOSE ==
            deviceStateForAggregation) {
          ToastHelper.showToast(wholeHouseDeviceAllCloseTip);
        } else if (onlineRoomCount > 0) {
          handleOperating();
          // 命令下发处理
          emitPowerCommand(false);
        } else {
          DevLogger.debug(
              tag: 'SmartHomeDevice', msg: 'turnOffAllDevices not match.');
        }
      }
    });
  }
}
