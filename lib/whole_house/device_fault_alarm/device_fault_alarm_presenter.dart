import 'package:smart_home/whole_house/device_fault_alarm/models/device_fault_alarm_response_model.dart';
import 'package:smart_home/whole_house/device_fault_alarm/store/device_fault_alarm_action.dart';
import 'package:smart_home/whole_house/whole_house_debounce.dart';

import '../../service/http_service.dart';
import '../../store/smart_home_store.dart';
import 'models/device_fault_alarm_item_model.dart';

/// 单例模式实现，确保全局只有一个实例
class DeviceFaultAlarmPresenter {
  // 单例实例
  static final DeviceFaultAlarmPresenter _instance = DeviceFaultAlarmPresenter._internal();
  
  // 工厂构造函数，返回单例实例
  factory DeviceFaultAlarmPresenter() {
    return _instance;
  }
  
  // 私有构造函数，防止外部直接实例化
  DeviceFaultAlarmPresenter._internal();
  
   /// 带防抖功能的故障数据查询方法
  void getWholeHouseDeviceFaultAlarmDataWithDebounce() {
    WholeHouseDebounce().faultDebounce.run(() {
      getWholeHouseDeviceFaultAlarmData();
    });
  }
  
  /// 查询故障数据
  Future<void> getWholeHouseDeviceFaultAlarmData() async {
    final String familyId = smartHomeStore.state.familyState.familyId;
    if (familyId.isEmpty) {
      return;
    }
    final DeviceFaultAlarmResponseModel? responseModel =
        await HttpService.getWholeHouseFaultAlarmResponseModel(familyId);
    if (responseModel is DeviceFaultAlarmResponseModel) {
      /// 将responseModel 组装为List<DeviceFaultAlarmItemModel
      final List<DeviceFaultAlarmItemModel> list = responseModel.list
          .map((DeviceFaultAlarmResponseItemModel item) =>
              DeviceFaultAlarmItemModel(
                  deviceId: item.deviceInfo.mac,
                  deviceName: item.deviceInfo.devName,
                  deviceImage: item.deviceInfo.deviceImage,
                  roomName: item.deviceInfo.devRoomName,
                  floorName: item.deviceInfo.devFloorName,
                  deviceFaults: item.deviceFaults ?? <DeviceFaultModel>[]))
          .toList();
      smartHomeStore
          .dispatch(UpdateWholeHouseDeviceFaultAlarmListStateAction(list));
    }
  }
}
