import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/location_weather/actions/area_actions.dart';
import 'package:smart_home/whole_house/location_weather/actions/outdoor_weather_actions.dart';
import 'package:smart_home/whole_house/location_weather/store/area_state.dart';
import 'package:smart_home/whole_house/location_weather/store/environment_state.dart';

/// 全屋状态的reducer，处理全屋相关的action
final Reducer<SmartHomeState> locationWeatherReducer = combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  // 处理天气状态相关的Action
  TypedReducer<SmartHomeState, UpdateWholeHouseOutdoorWeatherAction>(_updateOutdoorWeather).call,
  TypedReducer<SmartHomeState, ClearWholeHouseOutdoorWeatherAction>(_clearOutdoorWeather).call,
  
  // 处理区域状态相关的Action
  TypedReducer<SmartHomeState, UpdateWholeHouseAreaAction>(_updateWholeHouseArea).call,
]);

/// 更新室外天气状态
SmartHomeState _updateOutdoorWeather(
    SmartHomeState state, UpdateWholeHouseOutdoorWeatherAction action) {
  final EnvironmentState newEnvironmentState = state.wholeHouseState.environmentState.copyWith(
    outdoorWeatherState: action.weatherModel,
  );
  
  return state.copyWith(
    wholeHouseState: state.wholeHouseState.copyWith(
      environmentState: newEnvironmentState,
    ),
  );
}

SmartHomeState _clearOutdoorWeather(
    SmartHomeState state, ClearWholeHouseOutdoorWeatherAction action) {
  final EnvironmentState newEnvironmentState = state.wholeHouseState.environmentState.copyWith(
    clearOutdoorWeatherState: true,
  );
  return state.copyWith(
    wholeHouseState: state.wholeHouseState.copyWith(
      environmentState: newEnvironmentState,
    ),
  );
}

/// 更新区域状态
SmartHomeState _updateWholeHouseArea(
    SmartHomeState state, UpdateWholeHouseAreaAction action) {
  final AreaState newAreaState = state.wholeHouseState.areaState.copyWith(
    areaName: action.areaModel.areaName,
  );
  
  return state.copyWith(
    wholeHouseState: state.wholeHouseState.copyWith(
      areaState: newAreaState,
    ),
  );
}
