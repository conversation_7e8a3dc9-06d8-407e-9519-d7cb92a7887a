class GioConst {
  // MB36942 智家APP830-框架-展示智家Tab
  static const String smartHomeTabShow = 'MB36942';
  // MB36946 智家APP830-框架-下拉刷新  value: {智家、服务、商城、我的}
  static const String smartHomePullToRefresh = 'MB36946';
  // MB36965 智家APP830-框架-点击切换家庭
  static const String smartHomeSwitchFamily = 'MB36965';
  // MB36966 智家APP830-框架-点击家庭管理
  static const String smartHomeClickFamilyManager = 'MB36966';
  // MB36967 智家APP830-框架-点击右上角+号  value: {添加设备、扫一扫、创建场景、邀请家人}
  static const String smartHomeClickMenu = 'MB36967';
  // MBA38271 点击加号
  static const String smartHomeAddClick = 'MBA38271';
  // MB36968 智家APP830-框架-点击右上角客服按钮
  static const String smartHomeClickCustomer = 'MB36968';
  // 点击切换家庭
  static const String tabChangeFamilyClick = 'MBA38268';
  // MB38894 智家切换家庭提示弹窗曝光
  static const String smartHomeTabChangeFamilyTip = 'MB38894';

  // MB36998 智家T830-编辑-拖拽排序
  static const String smartHomeDragCardSort = 'MB36998';

  // MB37045 智家T830-未登录新手引导-欢迎语
  static const String newGuideWelcome = 'MB37045';
  // MB37046 智家T830-新手引导-跳过
  static const String newGuideSkip = 'MB37046';
  // MB37047 智家T830-新手引导-下一步
  static const String newGuideNext = 'MB37047';
  // MB37048 智家T830-新手引导-稍后再说
  static const String newGuideLater = 'MB37048';
  // MB37049 智家T830-新手引导-立即开启
  static const String newGuideStartNow = 'MB37049';

  // MB37141 智家APP830-框架-页面停留时长 参数：value / page_stay_time
  static const String smartHomePageStayTime = 'MB37141';

  // MB10411 智家APP830-框架-进入智家TAB
  static const String smartHomePageExpose = 'MB10411';

  /* ------- 编辑相关埋点 --------- */
  // 智家-设备区域-长按设备卡片
  static const String enterEditStatue = 'MB36990';
  // 智家APP2025-编辑-切换大小
  static const String gioChangeSize = 'MB38342';
  // 智家APP2025-编辑-移到顶部
  static const String gioMoveToTop = 'MB38343';
  // 智家APP2025-编辑-聚合卡片-编辑模式
  static const String gioAggregationSetting = 'MB38344';
  // 智家APP2025-编辑-聚合卡片-关闭聚合
  static const String gioCloseAggregation = 'MB38345';
  // 智家APP2025-编辑-拖拽排序
  static const String gioDragFinished = 'MB38346';
  // 智家-拖拽改变卡片大小
  static const String gioDragResizeFinished = 'MB38673';
  // MB36991 智家TAB-编辑-重命名
  static const String renameClick = 'MB36991';
  // MB36992 智家TAB-编辑-分享
  static const String shareClick = 'MB36992';
  // MB36993 智家TAB-编辑-转移房间
  static const String changePositionClick = 'MB36993';
  // MB36994 智家TAB-编辑-转移家庭
  static const String changeFamilyClick = 'MB36994';
  // MB36995 智家TAB-编辑-删除
  static const String deleteDeviceClick = 'MB36995';
  // 智家APP2025-编辑-完成
  static const String doneClick = 'MB37000';
  // 智家APP2025-编辑-取消
  static const String gioCancel = 'MB38347';
  // 智家APP2025-编辑-聚合二级页返回
  static const String gioEditBack = 'MB38674';
  // 智家APP2025-编辑-取消弹窗-确定
  static const String gioCancelDialogSure = 'MB38669';
  // 智家APP2025-编辑-取消弹窗-取消
  static const String gioCancelDialogCancel = 'MB38672';
  // MB37142 智家TAB-了解智家-未登录
  static const String understandApp = 'MB37142';

  // MB37050 智家TAB-了解智家-已登录
  static const String understandAppLogin = 'MB37050';

  // MB37051 智家TAB-新手礼包-已登录
  static const String newUserPackLogin = 'MB37051';

  // 家庭模型升级-特殊设备校验异常弹窗
  static const String gioCheckDialog = 'MBA38255';

  static const String csiCardPopUp= 'MB34490';
  static const String csiCardClose = 'MB34491';
  static const String csiCardSubmit = 'MB34492';
  static const String csiPopUpLabel = 'MB34493';
  static const String csiCardNoRemind = 'MB34494';
  static const String csiCardQuestion = 'MB35210';

  // 仪表盘
  // 点击仪表盘开关按钮
  static const String wholeHouseSwitch = 'MB39023';
  // 仪表盘图标展示
  static const String wholeHouseIconAppear = 'MB39024';
  // 仪表盘图标点击
  static const String wholeHouseIconClick = 'MB39025';
  // 仪表盘环境设备设置上报
  static const String wholeHouseEnvSettings = 'MB39026';
  // 仪表盘点击进入故障详情页面
  static const String wholeHouseClickFaultDetail = 'MB39031';
  // 智家APP2025-仪表盘-耗材进入耗材详情页
  static const String wholeHouseClickConsumableDetail = 'MB39027';
  // 智家APP2025-仪表盘-耗材-点击半屏窗按钮
  static const String wholeHouseClickConsumableListButton = 'MB39028';
  // 智家APP2025-仪表盘-耗材详情页-点击按钮
  static const String wholeHouseClickConsumableDetailButton = 'MB39029';
  // 智家APP2025-仪表盘-耗材-点击查看全屋耗材
  static const String wholeHouseClickToConsumableList = 'MB39030';

  /* ------- 聚合相关埋点 --------- */
  // 智家APP2025-智家TAB-设置-聚合按钮
  static const String aggSetting= 'MB38690';
  // 智家APP2025-聚合卡片-点击卡片
  static const String aggCard= 'MB38635';
  // 智家APP2025-聚合二级页-点击按钮
  static const String aggDetailBtn= 'MB38639';
  // 智家APP2025-聚合二级页-设置
  static const String aggDetailSetting= 'MB38643';
  // 智家APP2025-聚合二级页-设置-点击移除设备
  static const String aggDetailRemove= 'MB38644';
  // 智家APP2025-聚合二级页-设置-点击添加设备
  static const String aggDetailAdd= 'MB38645';
  // 智家APP2025-框架-操作引导-摄像头聚合
  static const String aggCameraSwitch= 'MB39033';
  // 智家APP2025-框架-操作引导-长期离线聚合
  static const String aggOfflineSwitch= 'MB39034';
  // 智家APP2025-框架-操作引导-不可联网聚合
  static const String nonNetSwitch= 'MB39035';
  // 自动切换家庭-点击弹窗切换按钮
  static const String autoSwitchFamily= 'MB38895';

  // 启动传参
  static const String launchParams = 'MB38990';
  // 家庭列表变化
  static const String familyMapUpdateComplete = 'MB38991';
  // 收到登录成功消息
  static const String userLoginSuccess = 'MB38993';
  // 收到退出登录消息
  static const String userLogoutMessage = 'MB38994';
  // 家庭详情刷新完成
  static const String familyDetailRefreshed = 'MB38995';
  // 筛选弹窗内房间管理点位
  static const String filterRoomManage = 'MB40069';
}

class DevListGioEvent {
  // MB38001 设备列表流程监控-t0设备列表变化 userId/curFamilyId
  static const String t0DevListChange = 'MB38001';
  // MB38002 设备列表流程监控-t0当前家庭变化 userId/curFamilyId
  static const String t0CurFamilyChange = 'MB38002';
  // MB38003 设备列表流程监控-t1获取设备列表 sizeAllDevList
  static const String t1GetDeviceInfoMap = 'MB38003';
  // MB38004 设备列表流程监控-t2组装设备模型 sizeAllDevList
  static const String t2PresenterDispatchDeviceMapAction = 'MB38004';
  // MB38005 设备列表流程监控-t3组装设备VM sizeAllDevList/sizeLargeCard/sizeSmallCard/sizeCameraCard/sizeWholeHouseCard
  static const String t3MiddleWareDispatchDeviceMapAction = 'MB38005';

  // event var
  // 当前用户id
  static const String userId = 'userId';
  // 当前家庭id
  static const String familyId = 'familyId';
  // 设备数量
  static const String sizeAllDevList = 'size_all_devList';
  // 大卡片数量
  static const String sizeLargeCard = 'size_large_card';
  // 小卡片数量
  static const String sizeSmallCard = 'size_small_card';
  // 摄像头卡片数量
  static const String sizeCameraCard = 'size_camera_card';
  // 全屋卡片数量
  static const String sizeWholeHouseCard = 'size_whole_house_card';
  // 当前选中导航tab
  static const String curNaviTab = 'curNaviTab';

  static const String loginStatus = 'loginStatus';

  static const String traceType = 'traceType';
}