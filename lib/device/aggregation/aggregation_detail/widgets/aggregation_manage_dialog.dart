import 'package:connectivity/connectivity.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/floor_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:smart_home/edit/util/edit_manager.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../../../common/device_filter_util.dart';
import '../../../../common/smart_home_util.dart';
import '../../../../widget_common/card_text_style.dart';
import '../../../device_info_model/smart_home_device_basic_info.dart';
import '../../../store/device_action.dart';
import '../../agg_store/aggregation_device_reducer_support.dart';
import '../../aggregation_card/view_model/aggregation_base_view_model.dart';
import '../../aggregation_card/view_model/aggregation_devices_by_room_model.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';
import '../../utils/agg_utils.dart';
import '../model/supportDeviceModel.dart';
import '../utils/aggregation_presenter.dart';
import '../view_model/detail_room_list.dart';
import '../view_model/detail_room_list_item.dart';
import '../view_model/manage_dialog_room.dart';
import 'aggregation_manage_content_widget.dart';

class FreeDevicesRoomModel {
  String roomId = '';
  String roomName = '';
  String floorId = '';
  String floorName = '';
  int floorOrder = 100; // 用于排序
  int roomOrder = 100; // 用于排序
  List<ManageDialogRoom> deviceList = <ManageDialogRoom>[];

  FreeDevicesRoomModel({
    required this.roomId,
    required this.roomName,
    required this.floorId,
    required this.floorName,
    required this.floorOrder,
    required this.roomOrder,
    required this.deviceList,
  });

  @override
  String toString() {
    return 'FreeDevicesRoomModel{roomId: $roomId, roomName: $roomName, '
        'floorId: $floorId, floorName: $floorName,floorOrder: $floorOrder, '
        'roomOrder: $roomOrder deviceList: $deviceList}';
  }
}

// 未聚合设备列表（按房间排序）
List<FreeDevicesRoomModel> _freeDevicesByRoom = <FreeDevicesRoomModel>[];

ValueNotifier<bool> _selectedNotifier = ValueNotifier<bool>(false);

// 标志位，用于记录是否已经调用过 dispose 方法
bool _isNotifierDisposed = false;
// 释放_selectedNotifier
void _disposeNotifier() {
  if (!_isNotifierDisposed) {
    _selectedNotifier.dispose();
    _isNotifierDisposed = true;
  }
}

void showAddDevicesBottomSheet(
    BuildContext context, String aggregationId, void Function() callback) {
  Connectivity().checkConnectivity().then((ConnectivityResult result) async {
    if (result == ConnectivityResult.none) {
      ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
    } else if (AggregationPresenter().loadingStatus ==
        AggLoadingStatus.normal) {
      final AggregationBaseViewModel viewModel = smartHomeStore
          .state
          .deviceState
          .allCardViewModelMap[aggregationId]! as AggregationBaseViewModel;

      _freeDevicesByRoom.clear();
      if (_isNotifierDisposed) {
        // 如果_selectedNotifier被释放了，这里直接用会报错，重新初始化。
        _selectedNotifier = ValueNotifier<bool>(false);
      }
      _selectedNotifier.value = false;
      const String dialogName = 'manageAggregationDialog';
      InterceptSystemBackUtil.interceptSystemBack(
          pageName: dialogName,
          callback: () {
            AggregationPresenter().hideLoading();
            // 释放_selectedNotifier
            _disposeNotifier();
            SmartHomeEditManager.closeEditDialog();
          });

      AggregationPresenter().showLoading(context);
      await setCheckedListMap(viewModel.deviceId);
      if (context.mounted) {
        if (_freeDevicesByRoom.isEmpty) {
          _showEmptyDialog(context, viewModel);
        } else {
          _showFreeDeviceListDialog(context, viewModel, callback);
        }
      }
    }
  });
}

void _showEmptyDialog(
    BuildContext context, AggregationBaseViewModel viewModel) {
  dialogs.showSingleBtnModal<SmartHomeState>(
    context: context,
    enableDrag: true,
    title: viewModel.manageDialogTitle,
    child: (BuildContext context) => _buildEmptyWidget(viewModel, context),
    btnText: '好的',
    callback: () {},
  );
}

Dialogs dialogs = Dialogs();

void _showFreeDeviceListDialog(BuildContext context,
    AggregationBaseViewModel viewModel, void Function() callback) {
  dialogs.showSmartHomeModalBottomSheet(
    context: context,
    headerConfig: HeaderConfig.title(viewModel.manageDialogTitle),
    child: (BuildContext context) {
      return StoreProvider<SmartHomeState>(
          store: smartHomeStore, child: _buildContentWidget(viewModel));
    },
    actionConfig: ActionConfig(<Widget>[
      ButtonFill.cancel(
        callback: () {
          dialogs.closeSmartHomeModalBottomSheet();
        },
      ),
      ValueListenableBuilder<bool>(
          valueListenable: _selectedNotifier,
          builder: (BuildContext context, bool isSelected, Widget? child) {
            return ButtonFill(
                text: '确定',
                enable: isSelected,
                callback: () {
                  _addClick(context, isSelected, viewModel, callback);
                });
          }),
    ], actionDirection: Axis.horizontal),
  );
}

Future<void> _addClick(BuildContext? context, bool isSelected,
    AggregationBaseViewModel viewModel, void Function() addCallback) async {
  if (!isSelected) {
    return;
  }

  try {
    // 检查网络连接
    if (!await checkNetworkConnection()) {
      return;
    }

    // 显示加载动画
    if (context != null && context.mounted) {
      AggregationPresenter().showLoading(context);
    }

    // 保存聚合设备ID列表
    AggregationPresenter.saveAggregationIdList(
        viewModel.deviceId, setAllDeviceIds(isSelected, viewModel));
    smartHomeStore.dispatch(AddAggDeviceAction(
        aggregationId: viewModel.deviceId,
        allDevices: setAllDeviceIds(isSelected, viewModel),
        sortedAggList: _saveAggSort(viewModel)));
    addCallback();
    await AggregationPresenter.saveAggSort(sortData: _saveAggSort(viewModel));

    AggregationPresenter().hideLoading();
    // 释放_selectedNotifier
    _disposeNotifier();
    dialogs.closeSmartHomeModalBottomSheet();
  } catch (e) {
    DevLogger.error(tag: 'AggregationManageDialog', msg: '_addClick error $e');
    AggregationPresenter().hideLoading();
    // 释放_selectedNotifier
    _disposeNotifier();
    dialogs.closeSmartHomeModalBottomSheet();
  }
}

AggDeviceData _saveAggSort(AggregationBaseViewModel vm) {
  final Map<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel> rooms =
      vm.devicesByRoom;
  final Map<String, List<String>> sortMap = <String, List<String>>{};
  _freeDevicesByRoom.forEach((FreeDevicesRoomModel roomModel) {
    final List<MapEntry<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel>>
        currentRoom = rooms.entries
            .where(
                (MapEntry<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel>
                        element) =>
                    element.key.roomId == roomModel.roomId)
            .toList();
    if (currentRoom.isNotEmpty) {
      for (final ManageDialogRoom data in roomModel.deviceList) {
        if (data.isSelected) {
          if (currentRoom[0].value.deviceList.contains(data.deviceId)) {
            currentRoom[0].value.deviceList.remove(data.deviceId);
          }
          currentRoom[0].value.deviceList.add(data.deviceId);
        }
      }
    } else {
      sortMap[roomModel.roomId] = roomModel.deviceList
          .where((ManageDialogRoom element) => element.isSelected)
          .map((ManageDialogRoom element) => element.deviceId)
          .toList();
    }
  });

  rooms.entries.forEach(
      (MapEntry<SmartHomeRoomInfo, AggregationDevicesByRoomViewModel> element) {
    sortMap[element.key.roomId] = <String>[...element.value.deviceList];
  });
  return AggregationPresenter.createAggDeviceData(vm.deviceId, sortMap);
}

List<String> setAllDeviceIds(
    bool isSelected, AggregationBaseViewModel viewModel) {
  // 获取已聚合设备列表
  final Set<String> allDeviceIds = <String>{};
  viewModel.devicesByRoom.forEach((SmartHomeRoomInfo room,
      AggregationDevicesByRoomViewModel deviceRoomModel) {
    allDeviceIds.addAll(deviceRoomModel.deviceList);
  });

  // 组装全量已聚合设备
  _freeDevicesByRoom.forEach((FreeDevicesRoomModel roomModel) {
    for (final ManageDialogRoom data in roomModel.deviceList) {
      if (data.isSelected && !allDeviceIds.contains(data.deviceId)) {
        allDeviceIds.add(data.deviceId);
      }
    }
  });

  DevLogger.info(
    tag: 'AggregationManageDialog',
    msg: '_addClick deviceId: ${viewModel.deviceId},'
        'allDeviceIds: $allDeviceIds',
  );

  return allDeviceIds.toList();
}

Future<void> setCheckedListMap(String deviceId) async {
  final SupportDeviceResModel? res =
      await AggregationPresenter.getDevicesByRoom(deviceId);
  AggregationPresenter().hideLoading();
  if (res is SupportDeviceResModel && res.retCode == '00000') {
    res.data.forEach((SupportDevModel element) {
      final List<ManageDialogRoom> deviceList =
          sortDeviceInRoom(element.deviceList).map((String deviceId) {
        return ManageDialogRoom(
          deviceId: deviceId,
        );
      }).toList();
      final List<FloorModel> floorListByFamily =
          Family.getCurrentFamilySync()?.floorInfos ?? <FloorModel>[];
      final Map<String, int> roomNameMapByFamily =
          buildRoomNameMap(floorListByFamily, getFloorName(element.floorName));
      if (deviceList.isNotEmpty) {
        // 设备列表为空的情况下不应该添加到列表中
        _freeDevicesByRoom.add(FreeDevicesRoomModel(
            roomId: element.roomId,
            roomName: element.roomName,
            floorId: element.floorId,
            floorName: element.floorName,
            floorOrder:
                floorNameMapWithOrderId[getFloorName(element.floorName)] ?? 100,
            roomOrder: roomNameMapByFamily[element.roomName] ?? 100,
            deviceList: deviceList));
      }
    });
    // 楼层房间排序
    _freeDevicesByRoom.sort((FreeDevicesRoomModel a, FreeDevicesRoomModel b) {
      if (a.floorOrder != b.floorOrder) {
        return a.floorOrder.compareTo(b.floorOrder);
      }
      return a.roomOrder.compareTo(b.roomOrder);
    });
  }
}

// 构建弹框内容Widget
Widget _buildEmptyWidget(
    AggregationBaseViewModel viewModel, BuildContext context) {
  return Container(
    width: MediaQuery.of(context).size.width,
    height: 148,
    padding: const EdgeInsets.symmetric(vertical: 12),
    child: Center(
      child: SizedBox(
        height: 124,
        child: Column(
          children: <Widget>[
            Image.asset(
              'assets/images/icon_aggregation_detail_no_device.webp',
              package: 'smart_home',
              width: 96,
              height: 96,
            ),
            const SizedBox(
              height: 8,
            ),
            Text(
              '暂无可添加设备',
              style: TextStyle(
                  fontSize: 14,
                  fontFamilyFallback: fontFamilyFallback(),
                  color: AppSemanticColors.item.secWeaken),
            ),
          ],
        ),
      ),
    ),
  );
}

// 构建弹框内容Widget
Widget _buildContentWidget(AggregationBaseViewModel viewModel) {
  return AggregationManageContentWidget(
      vm: viewModel,
      freeDevicesByRoom: _freeDevicesByRoom,
      callback: (List<FreeDevicesRoomModel> newList) {
        _freeDevicesByRoom = newList;
        _selectedNotifier.value = deviceSelect(_freeDevicesByRoom);
      });
}

bool deviceSelect(List<FreeDevicesRoomModel> list) {
  return list.any((FreeDevicesRoomModel item) {
    return item.deviceList.any((ManageDialogRoom room) {
      return room.isSelected;
    });
  });
}
