import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_consumables/store/device_consumables_action.dart';
import 'package:smart_home/whole_house/device_env/actions/env_devices_actions.dart';
import 'package:smart_home/whole_house/device_fault_alarm/store/device_fault_alarm_action.dart';
import 'package:smart_home/whole_house/whole_house_cache_model.dart';
import 'package:smart_home/whole_house/whole_house_presenter.dart';

import '../../whole_house/location_weather/actions/area_actions.dart';
import '../../whole_house/location_weather/actions/outdoor_weather_actions.dart';

class CacheMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    next(action);

    if (action is UpdateWholeHouseEnvDeviceStateAction ||
        action is UpdateWholeHouseDeviceConsumablesStateAction ||
        action is UpdateWholeHouseDeviceFaultAlarmListStateAction ||
        action is UpdateWholeHouseAreaAction ||
        action is UpdateWholeHouseOutdoorWeatherAction) {
      WholeHousePresenter.putWholeHouseDataToStorage(
          store.state.familyState.familyId,
          WholeHouseCacheModel(
              store.state.wholeHouseState.deviceFaultAlarmState.list,
              store.state.wholeHouseState.deviceConsumablesState.consumableMap,
              store.state.wholeHouseState.envDeviceState.spaces,
              store.state.wholeHouseState.areaState.areaName,
              store.state.wholeHouseState.environmentState.outdoorWeatherState));
    }
  }
}
