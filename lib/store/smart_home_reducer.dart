import 'package:redux/redux.dart';
import 'package:smart_home/cache/store/cache_reducer.dart';
import 'package:smart_home/device/device_guide/store/guide_reducer.dart';
import 'package:smart_home/device/store/device_reducer.dart';
import 'package:smart_home/scene/scene_viewmodel.dart';
import 'package:smart_home/smart_home_presenter.dart';
import 'package:smart_home/store/smart_home_action.dart';
import 'package:smart_home/user/user_reducer.dart';

import '../device/aggregation/agg_store/aggregation_reducer.dart';
import '../edit/store/edit_reducer.dart';
import '../navigator/family/store/family_reducer.dart';
import '../pack_gift/store/giftpack_reducer.dart';
import '../scene/store/scene_reducer.dart';
import '../whole_house/store/whole_house_reducer.dart';
import 'smart_home_state.dart';

final Reducer<SmartHomeState> smartHomeReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  cacheCombineReducer,
  userCombineReducer,
  family<PERSON>ombineReducer,
  editCombineReducer,
  deviceCombineReducer,
  wholeHouseCombineReducer,
  sceneCombineReducer,
  giftPackCombineReducer,
  aggregationCombineReducer,
  guideCombineReducer
]);
