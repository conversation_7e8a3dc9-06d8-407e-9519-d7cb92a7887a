import 'dart:math' as math;

import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/component_view_model/device_edit_component_view_model.dart';
import 'package:smart_home/device/component_widget/device_edit_component.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_presenter.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_widget/camera_ui_support.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:visibility_detector/visibility_detector.dart' as c;

enum CameraUIType {
  big_card,
  middle_card,
}

const double _radiuscircular = 22;
const double _radiusSmallCircule = 16;

const double _smallPlayButtonTop = 28;

class Camera extends StatefulWidget {
  String deviceId;

  ComponentBaseViewModel? selectedMode;
  int traceId = 0;

  CameraType cameraType = CameraType.HOME_CARD;

  CameraUIType uiType;

  Camera(this.deviceId, this.selectedMode, this.traceId,
      {super.key,
      this.cameraType = CameraType.HOME_CARD,
      this.uiType = CameraUIType.big_card});

  @override
  State<StatefulWidget> createState() {
    return CameraLiveState();
  }
}

class CameraLiveState extends State<Camera> with WidgetsBindingObserver {
  CameraLiveState();

  String tag = 'CameraLiveState';

  Store<SmartHomeState>? _store;

  CameraLivePresenter? presenter;

  // 本文样式
  static const TextStyle _textStyle = TextStyle(
      fontSize: 12.0, color: Colors.white, fontWeight: FontWeight.w400);

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    _store = StoreProvider.of<SmartHomeState>(context, listen: false);
    if (widget.cameraType == CameraType.HOME_CARD) {
      presenter =
          CameraLiveCoordinator.instance.getPresenterByDevId(widget.deviceId);
    } else {
      presenter = AggregationCameraPresenterManager.instance
          .getPresenterByDevId(widget.deviceId);
    }
    presenter?.init(smartHomeStore);
  }

  @override
  void didUpdateWidget(covariant Camera oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.deviceId != oldWidget.deviceId) {
      presenter?.stopPlayer(oldWidget.deviceId);
    }
  }

  double getClipSize() {
    if (widget.uiType == CameraUIType.middle_card) {
      return _radiusSmallCircule;
    }
    return _radiuscircular;
  }

  Widget _buildCameraContent(BuildContext ctx) {
    if (widget.uiType == CameraUIType.middle_card) {
      return buildMain(ctx);
    }
    return Column(
      children: <Widget>[buildMain(ctx), _buildCameraMsg()],
    );
  }

  Widget buildWrapper(BuildContext ctx) {
    return ClipRRect(
        borderRadius: BorderRadius.all(
          Radius.circular(getClipSize()),
        ),
        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: Colors.white, // 设置背景颜色
          ),
          child: Stack(
            children: <Widget>[
              _buildCameraContent(context),
              buildHeaderButtons(context, widget.selectedMode, widget.deviceId,widget.uiType),

              /// 编辑模式下的蒙层，处理点击事件，选中和去掉选中
              buildEditOverlay(
                child: GestureDetector(
                  onTap: () {
                    (widget.selectedMode! as DeviceEditComponentViewModel)
                        .selectedClick();
                  },
                  child: Container(
                    width: double.infinity,
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  Orientation? orientation;

  @override
  Widget build(BuildContext context) {
    orientation = MediaQuery.of(context).orientation;
    return buildWrapper(context);
  }

  Widget buildMain(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.all(
          Radius.circular(getClipSize()),
        ),
        child: StoreConnector<SmartHomeState, CameraVMWrapper>(
          distinct: true,
          converter: (Store<SmartHomeState> store) {
            final CameraDeviceCardViewModel card =
                store.state.deviceState.allCardViewModelMap[widget.deviceId]!
                    as CameraDeviceCardViewModel;
            return CameraVMWrapper(card, widget.cameraType);
          },
          builder: (BuildContext context, CameraVMWrapper vmw) {
            final CameraDeviceCardViewModel vm = vmw.vm;
            return maxLimitContainer(
                (375.w - 32) / 16 * 9,
                c.VisibilityDetector(
                  key: Key('visiblity_${widget.deviceId}'),
                  onVisibilityChanged: (c.VisibilityInfo visibilityInfo) {
                    final double visiblePercentage =
                        visibilityInfo.visibleFraction * 100;
                    if (visiblePercentage == 0) {
                      DevLogger.debug(
                          tag: tag,
                          msg:
                              'visiblePercentage = ${widget.deviceId} $hashCode ');
                      presenter?.stopPlayWhenDisappear(widget.deviceId);
                    }
                  },
                  child: Stack(
                    children: <Widget>[
                      player(vm),
                      Align(
                        child: SizedBox(
                          width: double.infinity,
                          height: double.infinity,
                          child: _statusWidget(
                              vm, double.infinity, double.infinity),
                        ),
                      ),
                      Align(
                          child: SizedBox(
                              width: double.infinity,
                              height: double.infinity,
                              child: _toolBarView(
                                  vm, double.infinity, double.infinity))),
                      _buildCameraHeader(vm),
                      buildEditOverlay(
                          child: InkWell(
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {},
                              child: SizedBox(
                                height: vm.playerHeight,
                                width: vm.playerWidth,
                              ))),
                    ],
                  ),
                ));
          },
        ));
  }

  Widget maxLimitContainer(double maxHeight, Widget child) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      child: child,
    );
  }

  Widget player(CameraDeviceCardViewModel vm) {
    if (vm.isShowPlayer(widget.cameraType)) {
      return SizedBox(
          height: vm.playerHeight,
          width: vm.playerWidth,
          child: _playingView(vm.playerHeight, vm.playerWidth));
    }
    return Container();
  }

  void _onSwitchCameraStream(
      CameraDeviceCardViewModel vm, CameraWidgetStyle style) {
    vm.gioSwitchCameraStream();
    presenter?.switchCameraStream(style, () {
      if (mounted) {
        setState(() {});
      }
    });
  }

  //编辑模式 Header
  Widget _buildCameraHeader(CameraDeviceCardViewModel vm) {
    final CameraWidgetStyle style = vm.getCurrentTyle(widget.cameraType);
    final bool isEdit = smartHomeStore.state.isEditState;
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          presenter?.stopPlayWhenDisappear(widget.deviceId);
          vm.gotoDetailPage(widget.deviceId, widget.cameraType);
        },
        child: SizedBox(
          height: 60,
          child: Stack(
            children: <Widget>[
              /// 顶部header的背景，当播放的时候再显示
              Visibility(
                visible: style == CameraWidgetStyle.playing,
                child: Image.asset(
                  vm.bgHeaderImageUrl,
                  width: double.infinity,
                  height: 60,
                  package: SmartHomeConstant.package,
                  fit: BoxFit.fill,
                ),
              ),
              getCameraHeader(widget.uiType, _deviceImage(vm), _deviceInfo(vm),
                  isEdit: isEdit,
                  streamType: vm.isOffline(widget.cameraType)
                      ? null
                      : presenter?.getCameraStreamIfNotSupport(), onTap: () {
                _onSwitchCameraStream(vm, style);
              }),
            ],
          ),
        ));
  }

  Widget _deviceImage(CameraDeviceCardViewModel vm) {
    if (widget.uiType == CameraUIType.middle_card) {
      return const SizedBox.shrink();
    }
    return getCameraDeviceImage(vm.deviceIcon);
  }

  Widget _deviceInfo(CameraDeviceCardViewModel vm) {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) =>
            store.state.deviceState.cardShowFloor,
        builder: (BuildContext context, bool showFloor) {
          return getCameraDeviceInfo(
              vm.title, '${showFloor ? vm.floor : ''}${vm.subTitle}');
        });
  }

  //播放器View
  Widget _playingView(double height, double width) {
    return Stack(
      children: <Widget>[
        Container(
          color: Colors.black,
        ),
        presenter!.currentPlayer(width, height,
            orientation: MediaQuery.of(context).orientation),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            presenter?.tapPlayer(widget.deviceId);
          },
          child: Container(),
        ),
      ],
    );
  }

  //状态view
  Widget _statusWidget(CameraDeviceCardViewModel vm, double w, double h) {
    Widget statusView = Container();
    switch (vm.getCurrentTyle(widget.cameraType)) {
      case CameraWidgetStyle.offline:
        statusView = _offlineView(vm);
        break;
      case CameraWidgetStyle.sleep:
        statusView = _sleepView(vm);
        break;
      case CameraWidgetStyle.stop:
        statusView = _startPlayView(vm);
        break;
      case CameraWidgetStyle.loading:
        statusView = _loadingView(vm);
        break;
      case CameraWidgetStyle.retry:
        statusView = _retryView(vm);
        break;
      case CameraWidgetStyle.playing:
        return Container();
      default:
        break;
    }

    return Stack(
      children: <Widget>[
        GestureDetector(
          onTap: () {
            if (vm.getCurrentTyle(widget.cameraType) ==
                CameraWidgetStyle.loading) {
              return;
            }
            presenter?.stopPlayWhenDisappear(widget.deviceId);
            vm.gotoDetailPage(widget.deviceId, widget.cameraType);
          },
          child: _backgroundImage(vm, w, h),
        ),
        SizedBox(
          height: h,
          width: w,
          child: statusView,
        ),
      ],
    );
  }

  //背景清晰的图 + 蒙层
  Widget _backgroundImage(CameraDeviceCardViewModel vm, double w, double h) {
    return Image.asset(
      vm.bgImageUrl,
      package: SmartHomeConstant.package,
      fit: BoxFit.cover,
      alignment: Alignment.topLeft,
      width: w,
      height: h,
    );
  }

  Widget _offlineView(CameraDeviceCardViewModel vm) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        vm.gotoDetailPage(widget.deviceId, widget.cameraType);
      },
      child: _statusViewWrapper(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Image.asset(
            vm.offlineIcon,
            width: 32,
            height: 32,
            package: SmartHomeConstant.package,
            fit: BoxFit.fill,
          ),
          const SizedBox(
            height: 8,
          ),
          Text(
            vm.offlineTips,
            style: _textStyle,
          ),
        ],
      )),
    );
  }

  Widget _startPlayView(CameraDeviceCardViewModel vm) {
    return _statusViewWrapper(
        child: InkWell(
          onTap: () {
            vm.gioPlayCLick();
            presenter?.playWrapper(widget.deviceId);
          },
          child: Image.asset(
            vm.playIcon,
            width: 36,
            height: 36,
            package: SmartHomeConstant.package,
            fit: BoxFit.fill,
          ),
        ),
        bottom: _smallPlayButtonTop);
  }

  Widget _loadingView(CameraDeviceCardViewModel vm) {
    return _statusViewWrapper(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox(
            width: 32,
            height: 32,
            child: Center(
              child: Lottie.asset(
                vm.loadingGifName,
                height: 24,
                width: 24,
                package: SmartHomeConstant.package,
                key: const Key('camera_loading_card_image'),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 8,
            ),
            child: Text(vm.loadingTips, style: _textStyle),
          ),
        ],
      ),
    );
  }

  Widget _retryOrSleepView(String prompt, String icon, void Function() action) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        action();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Image.asset(
            icon,
            width: 32,
            height: 32,
            package: SmartHomeConstant.package,
            fit: BoxFit.fill,
          ),
          const SizedBox(
            height: 8,
          ),
          Text(
            prompt,
            style: _textStyle,
          ),
        ],
      ),
    );
  }

  Widget _sleepView(CameraDeviceCardViewModel vm) {
    return _statusViewWrapper(
      child: _retryOrSleepView(vm.sleepTips, vm.sleepImageUrl, () {
        presenter?.wakeupWrapper(widget.deviceId);
      }),
    );
  }

  Widget _retryView(CameraDeviceCardViewModel vm) {
    return _statusViewWrapper(
      child: _retryOrSleepView(vm.retryTips, vm.loadingErrImageUrl, () {
        vm.gioPlayCLick();
        presenter?.retryWrapper(widget.deviceId);
      }),
    );
  }

  Widget _toolBarView(CameraDeviceCardViewModel vm, double w, double h) {
    return SizedBox(
      width: w,
      height: h,
      child: Visibility(
        visible: vm.getCameraToolsBarWithType(widget.cameraType),
        child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              presenter?.tapToolsBar(widget.deviceId);
            },
            child: _createPlayerButton(vm)),
      ),
    );
  }

  Widget _createRightPaddingWidget(Widget child, void Function()? onClick) {
    return Positioned(
      right: 16,
      bottom: 10,
      child: GestureDetector(
        onTap: () {
          onClick?.call();
        },
        child: child,
      ),
    );
  }

  Widget _createCenterPaddingWidget(Widget child, VoidCallback onClick) {
    return Positioned(
      bottom: 10,
      child: Align(
        child: GestureDetector(
          onTap: () {
            onClick();
          },
          child: child,
        ),
      ),
    );
  }

  void onPauseClick(CameraDeviceCardViewModel vm) {
    vm.gioPauseCLick();
    presenter?.tapPause(widget.deviceId);
  }

  void onFullscreenClick(CameraDeviceCardViewModel vm) {
    if (mounted) {
      vm.gioFullscreenClick();
      presenter?.tapFullscreen(context, refreshCamera);
    }
  }

  void refreshCamera() {
    if (mounted) {
      setState(() {});
    }
  }

  Widget _createIconButtonImage(String url) {
    return Image.asset(
      url,
      width: 32,
      height: 32,
      package: SmartHomeConstant.package,
      fit: BoxFit.fill,
    );
  }

  Widget _createPlayerButton(CameraDeviceCardViewModel vm) {
    final bool supportFullscreen = vm.supportFullScreenMode(widget.uiType);

    final Widget pauseContent = _createIconButtonImage(vm.pauseUrl);

    final Widget pauseWidget = supportFullscreen
        ? _createCenterPaddingWidget(pauseContent, () {
            onPauseClick(vm);
          })
        : _createRightPaddingWidget(pauseContent, () {
            onPauseClick(vm);
          });

    final Widget fullScreenContent =
        _createIconButtonImage(CameraConstant.iconFullScreen);

    final Widget fullScreenWidget = supportFullscreen
        ? _createRightPaddingWidget(fullScreenContent, () {
            onFullscreenClick(vm);
          })
        : Container(
            height: 0,
          );

    final List<Widget> content = <Widget>[
      Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Transform.rotate(
          angle: math.pi,
          child: Image.asset(
            vm.bgHeaderImageUrl,
            width: double.infinity,
            height: 52,
            package: SmartHomeConstant.package,
            fit: BoxFit.fill,
          ),
        ),
      ),
      Positioned(
        bottom: 10,
        left: 16,
        child: GestureDetector(
            onTap: () {
              vm.gioMuteCLick();
              presenter?.tapMute(widget.deviceId, !vm.isMute);
            },
            child: _createIconButtonImage(vm.muteIcon)),
      ),
    ];

    content.add(pauseWidget);
    content.add(fullScreenWidget);

    return Stack(alignment: Alignment.center, children: content);
  }

  Widget imageBtnWrapper(Image c, double height, double width, double border,
      {bool useWhiteBg = false}) {
    return Container(
        child: Center(
          child: c,
        ),
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: useWhiteBg ? Colors.white : const Color(0x99999999),
          borderRadius: BorderRadius.circular(border),
        ));
  }

  Widget _statusViewWrapper(
      {required Widget child, double bottom = defaultTop}) {
    if (widget.uiType == CameraUIType.middle_card) {
      return Padding(
        padding: EdgeInsets.only(bottom: bottom),
        child: Align(
          child: child,
          alignment: Alignment.bottomCenter,
        ),
      );
    }
    return Center(child: child);
  }

  void onPageHide() {
    presenter?.stopPlayWhenDisappear(widget.deviceId);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.detached:
        return;
      case AppLifecycleState.resumed:
        return;
      case AppLifecycleState.inactive:
        return;
      case AppLifecycleState.hidden:
        onPageHide();
      case AppLifecycleState.paused:
        return;
    }
  }

  @override
  void dispose() {
    DevLogger.debug(
        tag: tag,
        msg: 'dispose camera = ${widget.deviceId} ${presenter?.playerMetux}');
    WidgetsBinding.instance.removeObserver(this);
    if (orientation == Orientation.portrait) {
      presenter?.stopIndeed();
    }
    super.dispose();
  }

  Widget _buildCameraMsg() {
    return StoreConnector<SmartHomeState, CameraMsgVmWrapper>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          final CameraDeviceCardViewModel card =
              store.state.deviceState.allCardViewModelMap[widget.deviceId]!
                  as CameraDeviceCardViewModel;
          return CameraMsgVmWrapper(card.getCameraMsgVM(),widget.cameraType);
        },
        builder: (BuildContext context, CameraMsgVmWrapper vm) {
          if (vm.isValid()) {
            return CameraMsgListWidget(vm.vm, widget.deviceId,widget.cameraType);
          }
          return Container(height: 0);
        });
  }
}

class CameraMsgListWidget extends StatelessWidget {
  final CameraMsgVM vm;
  final String devId;
  final CameraType type;

  const CameraMsgListWidget(this.vm, this.devId,this.type, {super.key});

  @override
  Widget build(BuildContext context) {
    final double width = ((375.w - 32) - 12 * 5 - 28) / 3;
    final double height = width / 16 * 9;

    final List<Widget> tmp = <Widget>[];
    tmp.add(Container(
      width: 12,
    ));
    for (int i = 0; i < vm.msgs.length; i++) {
      tmp.add(Container(
          height: height,
          width: width,
          margin: const EdgeInsets.only(right: 12),
          child: createItem(vm.msgs[i], vm.showBlur(type), width, height)));
    }
    tmp.add(
      Expanded(child: Container()),
    );
    tmp.add(GestureDetector(
      child: Container(
        width: 28,
        margin: const EdgeInsets.only(top: 12, bottom: 12, right: 12),
        decoration: BoxDecoration(
          color: const Color(0xfff0f0f0),
          borderRadius: BorderRadius.circular(12), // 设置圆角为 12
        ),
        child: Center(
          child: Image.asset(
            'assets/icons/arrow_black.png',
            package: 'smart_home',
            width: 12,
            height: 12,
          ),
        ),
      ),
      onTap: () {
        vm.goToCameraMsgDetailPage(devId);
      },
    ));

    return SizedBox(
      height: (375.w - 32) / 16 * 9 * 0.375,
      child: Row(children: tmp),
    );
  }

  Widget createHolder(double width, double height) {
    return Image.asset(
      CameraConstant.bgImageUrl,
      package: SmartHomeConstant.package,
      fit: BoxFit.cover,
      alignment: Alignment.topLeft,
      width: width,
      height: height,
    );
  }

  Widget _itemClickHolder(Widget widget, CameraMsgItem item) {
    return GestureDetector(
      child: widget,
      onTap: () {
        vm.goToCameraMsgDetailPageFromAlarmTime(devId, item.timeStamp);
      },
    );
  }

  Widget createItem(
      CameraMsgItem item, bool isBlur, double width, double height) {
    if (isBlur) {
      return _itemClickHolder(
          ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: <Widget>[
                  createHolder(width, height),
                  Positioned(
                      child: Text(item.title,
                          style: const TextStyle(
                              color: Colors.white, fontSize: 10)),
                      left: 6,
                      bottom: 6)
                ],
              )),
          item);
    }
    return _itemClickHolder(
        ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              children: <Widget>[
                CommonNetWorkImage(
                  url: item.imageUrl,
                  width: width,
                  height: height,
                  needReload: 2,
                  withOssParam: false,
                  fit: BoxFit.fill,
                  errorWidget: createHolder(width, height),
                ),
                Positioned(
                    child: Text(
                      item.title,
                      style: const TextStyle(color: Colors.white, fontSize: 10),
                    ),
                    left: 6,
                    bottom: 6)
              ],
            )),
        item);
  }
}
