import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/component_view_model/wind_speed_component_view_model.dart';
import 'package:smart_home/device/component_widget/wind_speed_component/division_slider_overlay_shape.dart';
import 'package:smart_home/device/component_widget/wind_speed_component/division_slider_track_shape.dart';

import '../../../common/component_constant.dart';
import '../../../store/smart_home_store.dart';

class WindSpeedComponent extends StatefulWidget {
  const WindSpeedComponent({super.key, required this.viewModel});

  final WindSpeedComponentViewModel viewModel;

  @override
  State<WindSpeedComponent> createState() => _WindSpeedComponentState();
}

class _WindSpeedComponentState extends State<WindSpeedComponent> {
  double selectedValue = 0;

  Timer? _timerEnd;

  Timer? _loopTimer;

  bool _operating = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      selectedValue = widget.viewModel.selectedValue;
    });
  }

  @override
  void didUpdateWidget(covariant WindSpeedComponent oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {
      selectedValue = widget.viewModel.selectedValue;
    });
  }

  @override
  void dispose() {
    timerCancel();
    super.dispose();
  }

  void timerCancel() {
    if (_loopTimer != null) {
      _loopTimer!.cancel();
    }
  }

  void startCountTimer() {
    const Duration duration = Duration(seconds: 1);

    timerCancel();

    _loopTimer = Timer(duration, () {
      // 用户操作结束后再进行页面刷新
      if (!_operating) {
        setState(() {
          selectedValue = widget.viewModel.selectedValue;
        });
      }
      timerCancel();
      startCountTimer();
    });
  }

  void handleValueChange(double v) {
    _timerEnd?.cancel();
    setState(() {
      selectedValue = v;
      _operating = true;
    });
  }

  void handleValueChangeEnd() {
    if (widget.viewModel.onChangeEnd != null) {
      widget.viewModel.onChangeEnd!(selectedValue);
    }
    _timerEnd = Timer(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _operating = false;
        });
      }
      _timerEnd = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final bool isComponentEnabled =
        widget.viewModel.enable && !smartHomeStore.state.isEditState;
    final double opacity =
        isComponentEnabled ? ComponentOpacity.enable : ComponentOpacity.disable;
    return Opacity(
      opacity: opacity,
      child: Padding(
        padding: const EdgeInsets.only(
            top: ComponentPadding.middle,
            left: ComponentMargin.page,
            right: ComponentMargin.page,
            bottom: ComponentPadding.middle),
        child: Column(
          children: <Widget>[
            if (widget.viewModel.title != null &&
                widget.viewModel.title!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: ComponentPadding.middle),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: SmartHomeText(
                      text: widget.viewModel.title!,
                      fontSize: 12,
                      color: AppSemanticColors.item.secondary),
                ),
              ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: SizedBox(
                height: 81,
                child: SliderTheme(
                  //自定义风格
                  data: SliderTheme.of(context).copyWith(
                      trackShape: DivisionSliderTrackShape(
                          selectedValue,
                          widget.viewModel.valueList,
                          AppSemanticColors.item.information.primary,
                          AppSemanticColors.item.primary,
                          isAlign: widget.viewModel.isAlign,
                          radius: ComponentRadius.button),
                      overlayShape: const DivisionSliderOverlayShape(
                        overlayRadius: 5,
                      ),
                      thumbColor: Colors.white,
                      thumbShape:
                          const RoundSliderThumbShape(enabledThumbRadius: 16),
                      inactiveTickMarkColor: Colors.transparent,
                      activeTickMarkColor: Colors.transparent,
                      trackHeight: 8),
                  child: Slider(
                    value: selectedValue,
                    onChanged: (double v) {
                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call(widget.viewModel.attribute)
                            .then((bool pass) {
                          if (pass) {
                            handleValueChange(v);
                            setState(() {});
                          }
                        });
                      } else {
                        handleValueChange(v);
                        setState(() {});
                      }
                    },
                    onChangeEnd: (double v) {
                      if (widget.viewModel.checkContinue != null) {
                        widget.viewModel.checkContinue
                            ?.call(widget.viewModel.attribute)
                            .then((bool pass) {
                          if (pass) {
                            handleValueChangeEnd();
                          }
                        });
                      } else {
                        handleValueChangeEnd();
                      }
                    },
                    divisions: widget.viewModel.valueList.isNotEmpty
                        ? widget.viewModel.valueList.length - 1
                        : null,
                    max: (widget.viewModel.valueList.isNotEmpty
                            ? widget.viewModel.valueList.length - 1
                            : 0)
                        .toDouble(),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
