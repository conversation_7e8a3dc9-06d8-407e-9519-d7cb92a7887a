import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

import '../device/resize_device_card/resize_base_model.dart';

class EditBottomViewModel {
  EditBottomViewModel(
      this.editBtnList, this.cardSizeList, this.placeHolderText);

  String placeHolderText = '';

  List<EditBtnInfoViewModel> editBtnList = <EditBtnInfoViewModel>[];

  List<CardSizeOptionViewModel> cardSizeList = <CardSizeOptionViewModel>[];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EditBottomViewModel &&
          runtimeType == other.runtimeType &&
          placeHolderText == other.placeHolderText &&
          listEquals(editBtnList, other.editBtnList) &&
          listEquals(cardSizeList, other.cardSizeList);

  @override
  int get hashCode =>
      placeHolderText.hashCode ^
      listHashCode(editBtnList) ^
      listHashCode(cardSizeList);

  @override
  String toString() {
    return 'EditBottomViewModel{placeHolderText: $placeHolderText, editBtnList: $editBtnList, '
        'cardSizeList: $cardSizeList}';
  }
}

enum ButtonActionType {
  /// 完成
  finish,

  /// 更多
  more,

  /// 切换大小
  changeSize,

  /// 移到顶部
  moveToTop,

  /// 共享
  share,

  /// 更改房间
  changeDevicePosition,

  /// 重命名
  rename,

  /// 转移家庭
  changeDeviceFamily,

  /// 删除设备
  deleteDevice,

  /// 关闭聚合
  closeAggregation,

  /// 编辑
  aggregationSetting,

  /// 移出（场景）
  sceneRemove,

  /// 编辑（场景）
  sceneEdit,
}

class EditBtnInfoViewModel {
  ButtonActionType actionType;
  String name = '';
  String icon = '';

  EditBtnInfoViewModel(this.actionType, this.name, this.icon);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is EditBtnInfoViewModel &&
              runtimeType == other.runtimeType &&
              actionType == other.actionType &&
              name == other.name &&
              icon == other.icon;
  @override
  int get hashCode =>
      actionType.hashCode ^ name.hashCode ^ icon.hashCode;

  @override
  String toString() {
    return 'EditBtnInfoViewModel{actionType: $actionType, name: $name, icon: $icon}';
  }
}

class CardSizeOptionViewModel {
  String name = ''; // 尺寸名称
  DeviceCardType type = DeviceCardType.middleCard;
  bool actived = false; // 选中状态

  CardSizeOptionViewModel({
    required this.name,
    required this.type,
    required this.actived,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is CardSizeOptionViewModel &&
              runtimeType == other.runtimeType &&
              name == other.name &&
              type == other.type &&
              actived == other.actived;
  @override
  int get hashCode =>
      name.hashCode ^ actived.hashCode ^ type.hashCode;

  @override
  String toString() {
    return 'CardSizeOptionViewModel{name: $name, type: $type,'
        'actived: $actived}';
  }
}
