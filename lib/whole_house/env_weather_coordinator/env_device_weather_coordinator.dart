import '../../store/smart_home_store.dart';
import '../device_env/actions/env_devices_actions.dart';
import '../device_env/services/env_devices_response_model.dart';
import '../location_weather/location_weather_presenter.dart';
import '../location_weather/models/outdoor_weather_model.dart';
import '../location_weather/services/location/location_model.dart';

/// 环境和天气数据协调器，确保这两种数据同时更新UI
class EnvDeviceWeatherCoordinator {
  /// 环境数据
  EnvDevicesData? envDevicesData;

  /// 位置数据
  LocationModel? location;

  /// 天气数据
  OutdoorWeatherModel? outdoorWeatherModel;

  /// 设置环境数据
  void setEnvData(EnvDevicesData? data) {
    envDevicesData = data;
  }

  /// 设置位置数据
  void setLocationData(LocationModel? data) {
    location = data;
  }

  /// 设置天气数据
  void setWeatherData(OutdoorWeatherModel? data) {
    outdoorWeatherModel = data;
  }

  /// 更新状态：优先环境设备，没有环境的话更新天气和天气的位置
  void updateBothStates() {
    if (envDevicesData != null) {
      smartHomeStore.dispatch(
          UpdateWholeHouseEnvDeviceStateAction(envDevicesData!.spaces));
    }

    LocationWeatherPresenter()
        .updateLocationAndWeatherData(location, outdoorWeatherModel);
  }
}
