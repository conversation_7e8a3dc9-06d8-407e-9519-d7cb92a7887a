import 'package:flutter/foundation.dart';

/// 室外天气展示组件的ViewModel
class OutdoorWeatherVM {
  /// 温度
  final String temperature;
  
  /// 天气图标标识
  final String icon;
  
  /// 天气现象描述
  final String phenomenon;
  
  /// 风向
  final String windDirection;
  
  /// 风力
  final String windPower;
  
  /// 湿度
  final String humidity;
  
  /// 是否有数据
  final bool hasData;
  
  /// 刷新回调
  final VoidCallback onRefresh;
  
  /// 构造函数
  const OutdoorWeatherVM({
    required this.temperature,
    required this.icon,
    required this.phenomenon,
    required this.windDirection,
    required this.windPower,
    required this.humidity,
    required this.hasData,
    required this.onRefresh,
  });
  
  /// 比较两个ViewModel是否相等，用于distinct优化
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OutdoorWeatherVM &&
        other.temperature == temperature &&
        other.icon == icon &&
        other.phenomenon == phenomenon &&
        other.windDirection == windDirection &&
        other.windPower == windPower &&
        other.humidity == humidity &&
        other.hasData == hasData;
  }
  
  @override
  int get hashCode => 
      temperature.hashCode ^ 
      icon.hashCode ^ 
      phenomenon.hashCode ^
      windDirection.hashCode ^
      windPower.hashCode ^
      humidity.hashCode ^
      hasData.hashCode;
} 