class SmartHomeConstant {
  static const String largeCardFuncAreaOne = 'funcAreaOne';
  static const String package = 'smart_home';
  static const String fontArial = 'Arial';
  static const String fontPingFangSCLight = 'PingFangSC-Light';
  static const String fontPingFangSCRegular = ' PingFangSC-Regular';
  static const String smartHomeAppBrand = 'smart_home_app_brand';
  static const String fisher = 'fisher';
  static const String casarte = 'casarte';
  static const String haier = 'haier';
  static const String leader = 'leader';
  static const String netType = 'netType_9.0.0';
  static const String userGuideStatus = 'userGuideStatus_version_2_';
  static const String userGuideFamilyStatus = 'userGuideStatus_version_family_';

  static const String shareDeviceFlag = 'f3b2c5d8-7a9e-41b3-8d4f-2e5a7b8c9d0e';
  static const String shareDeviceTabText = '共享设备';

  static const int cardFuncTitleFonSize = 14;

  static const String key_alert_cancel_default = 'alarmCancel';

  static const String vdnDeviceDetail =
      'http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?deviceId=';

  static const String vdnWaitingForNetwork = 'mpaas://quickConnect?deviceId=';

  static const String wholeHousePage = 'flutter://SmartHomeWholeHouse.html';

  static const String aggregationSettingPage = 'flutter://smarthome/settings';

  static const String sceneManagePage = 'flutter://smart_home/scene_manage';

  // 房间管理页
  static const String roomManager = 'mpaas://familymanage#/roomList';

  // 跳转全屋信息二级页
  static const String wholeHouseUrl =
      'mpaas://wholeHouse?needAuthLogin=1&familyId=';
  static const String deviceGuideUrl = 'flutter://deviceGuide.html';
  static const String waterPage = 'flutter://WholeHouseWater.html';
  static const String heatingPage = 'flutter://WholeHouseHeating.html';
  static const String airPage = 'flutter://WholeHouseAir.html';

  /// 单耗材详情页
  static const String consumableDetailPage =
      'flutter://smart_home/consumable_detail_page';

  // 登录url
  static const String loginUrl = 'mpaas://usercenter';

  static const String wifiSettingVdn = 'mpaas://quickConnect?deviceId=';

  static const String urlInstallRepair =
      'https://service.haiersmarthomes.com/app/index.html?type=T02&code=303&underneathStatusBar=1&push=outSide&needAuthLogin=1#/userServices';

  static const String vdnBindPage =
      'http://uplus.haier.com/uplusapp/main/qrcodescan.html?needAuthLogin=1';

  static const String vdnBindGuidePage =
      'https://zjrs.haier.net/zjapp/bindingHelp/index.html?container_type=3#/videoGuide';

  //采暖炉预约、热泵预约用水二级页后缀
  static const String quickControlStoveReservationSuffix =
      '&businessType=cardInterGoAppointPage&needAuthLogin=1';

  static const String sceneMinePage = 'flutter://SceneMine.html';
  static const String sceneRecommendPage = 'flutter://SceneRecommend.html';

  static const String vdnAutoCreateCardBind =
      'https://uplus.haier.com/uplusapp/bind/reportrepairentrance.html';

  // 设备属性
  static const String deviceAttrTrue = 'true';
  static const String deviceAttrFalse = 'false';
  static const String deviceOnOffStatus = 'onOffStatus';

  static const String deviceAttrName = 'name';
  static const String deviceAttrValue = 'value';

  static const List<String> promptsCSIList = <String>[
    '',
    '很差',
    '差',
    '一般',
    '满意',
    '很满意'
  ];
  static const Map<int, String> scoreMap = <int, String>{
    1: '一星',
    2: '二星',
    3: '三星',
    4: '四星',
    5: '五星',
  };

  static const String filter = '筛选设备';

  static const String NETWORK_ERROR_TIP = '网络不可用';

  static const String logicEngineOperateSuccessCode = '000000';

  static const String zjServerRetSuccessCode = '00000';

  static const String temperatureLabelDesc = '温度设置';
  static const String temperatureControlDesc = '温度控制';
  static const String constantTemperatureLabelDesc = '恒温设置';
  static const String constantTemperatureControlDesc = '恒温控制';
  static const String toastDevicePowerOffOrOffline = '已关机或未联网，无法操作';
  static const String toastDevicePowerOff = '设备关机，无法操作';
  static const String toastDeviceOfflineInfo = '设备离线，无法操作';
  static const String toastDeviceLoadingInfo = '设备连接中，请稍候';
  static const String deviceFilterSelectAll = '全部';
  static const String deviceFilterHouse = '全屋';
  static const String toastDeviceOnlineNotReadyInfo = '设备连接中，请稍候';
  static const String toastDeviceAlarmInfo = '设备故障，请检查设备状态';
  static const String toastNotWritable = '操作失败';
  static const String toastChildLockOnMsg = '请关闭童锁后操作';
  static const String toastFailure = '操作失败';
  static const String toastPowerOff = '请在本机端通电';
  static const String toastWakeUp = '请在本机端唤醒';
  static const String toastWashAuthorizeAtMachine = '远程授权不可用，请在本机完成授权';
  static const String toastWashAuthorizeAtSetting = '远程授权不可用，请在设置页完成授权';
  static const String toastUnsupportedStopProgramInApp = '不支持结束程序，请在本机端关机';
  static const String toastSelectProgram = '请选择要执行的程序';
  static const String toastFlushing = '设备正在冲洗中';
  static const String casarteDevice = '卡萨帝';
  static const String fisherDevice = 'Fisher';
  static const String leaderDevice = 'Leader';
  static const String toastAirPowerOffInfo = '空调关机，无法操作';
  static const String toastAirModeDisable = '暂时无法选择模式';
  static const String toastAirWindDisable = '当前状态无法调节风速';
  static const String songBoxPreSong = '上一曲';
  static const String songBoxNextSong = '下一曲';
  static const String songBoxPlay = '播放';
  static const String songBoxPause = '暂停';
  static const String noOffLineDevice = '暂无长期离线设备';
  static const String noNonNetDevice = '暂无不可连网设备';

  static const String familyRoleWarning = '请联系家庭管理员添加设备或场景';
  static const String contactFamilyRole = '请联系家庭管理员进行操作';
  static const String adminCountLimit = '管理员数量已达上限';
  static const String memberCountLimit = '成员数量已达上限';
  static const String cardManageWarning = '请联系家庭管理员编辑卡片';
  static const String contactFamilyRoleWarning = '请联系家庭管理员修改';

  /// [baseUrl] 智家请求接口域名
  static const String baseUrl = 'https://zj.haier.net';

  static const String deviceFilterGio = 'MB37001';
  static const String deviceFilterCategoryGio = 'MB37002';
  static const String deviceFilterRoomGio = 'MB37003';
  static const String deviceFilterFloorGio = 'MB37004';
  static const String deviceFilterResetGio = 'MB37005';
  static const String deviceFilterConfirmGio = 'MB37006';
  static const String deviceListExposureGio = 'MB36970';
  static const String quickListExposureGio = 'MB36972';
  static const String deviceListClickGio = 'MB36971';
  static const String quickListClickGio = 'MB36973';
  static const String largeCardClickGio = 'MB36980';
  static const String middleAndSmallCardClickGio = 'MB36983';
  static const String deviceCardOnOffGio = 'MB36981';
  static const String middleCardOnOffGio = 'MB36984';
  static const String modelEmptyGio = 'MB36986';
  static const String alarmGio = 'MB36985';
  static const String waitingForNetworkGio = 'MB36989';
  static const String reportForRepairGio = 'MB37052';
  static const String addDeviceCardClickGio = 'MB37053';
  static const String addDeviceCardBindGuideClickGio = 'MB37054';

  static const String unLoginNewUserPackLocation = 'B0357';
  static const String loginNewUserPackLocation = 'B0358';

  static const String wholeHouseInfoExposeGio = 'MB37055';
  static const String wholeHouseInfoClickGio = 'MB37056';
  static const String wholeHouseConsumeExposeGio = 'MB37058';
  static const String wholeHouseConsumeClickGio = 'MB37059';

  static const String sceneRecommendClickGio = 'MB37212';
  static const String sceneAlreadyLaunchClickGio = 'MB36938';
  static const String sceneAllSceneClickGio = 'MB36939';
  static const String sceneMoreSceneClickGio = 'MB37213';

  static const String heatingStovePowerOffDialog = 'MB38353';
}

class SwitchStatusConstant {
  static const String switchOn = '开';
  static const String switchOff = '关';
  static const String switchUnknown = '-';
}
