import 'package:connectivity/connectivity.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:library_widgets/common/constant.dart';
import 'package:library_widgets/common/manual_operation_overlay.dart';
import 'package:redux/redux.dart';
import 'package:storage/storage.dart';
import '../../common/constant.dart';
import '../../edit/store/edit_action.dart';
import '../../store/smart_home_state.dart';
import '../scene_presenter.dart';
import 'scene_action.dart';

class SceneMiddleware implements MiddlewareClass<SmartHomeState> {
  final ScenePresenter _scenePresenter = ScenePresenter();

  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) async {
    if (action is SceneExecuteAction) {
      final ConnectivityResult connectivityResult =
          await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
        return;
      }
      if (action.item.isInvalid) {
        ToastHelper.showToast('设备被移除，场景已失效');
        return;
      }

      await Storage.setTemporaryStorage(
          CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET, '1');

      await Storage.setTemporaryStorage(
          CONSTANT.DEVICE_SCENE_TABTYPE_INDEX, 'smart_home');

      if (action.context.mounted) {
        ManualOperation.execute(<String, String>{
          'id': action.item.sceneId,
          'sceneName': action.item.sceneName,
          'familyId': store.state.familyState.familyId
        }, connectivityResult != ConnectivityResult.none,
            store.state.familyState.familyId, action.context, 'ismodel',
            tabtype: 'smart_home');
      }
      return;
    } else if (action is ExitEditStateAction) {
      // TODO-zqj 退出编辑，不请求场景列表，会导致场景列表闪烁-后续优化退出编辑流程
      // _scenePresenter.fetchSceneData(store.state.familyState.familyId);
    }
    next(action);
  }
}
