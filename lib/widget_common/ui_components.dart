/*
 * 描述：通用UI组件
 * 作者：fancunshuo
 * 建立时间: 2025/5/13
 */
import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:gradient_borders/gradient_borders.dart';

class CommonUIComponents {
  /// 创建弹窗容器的渐变边框装饰器
  static BoxDecoration dialogGradientBorderDecoration = BoxDecoration(
    color: AppSemanticColors.container.menu,
    borderRadius: const BorderRadius.all(
      Radius.circular(16),
    ),
    border: gradientBorder,
  );

  static const GradientBoxBorder gradientBorder = GradientBoxBorder(
    gradient: LinearGradient(
      colors: <Color>[
        Color.fromRGBO(255, 255, 255, 0.80),
        Color.fromRGBO(255, 255, 255, 0.00),
        Color.fromRGBO(255, 255, 255, 0.00),
        Color.fromRGBO(255, 255, 255, 0.60)
      ],
      begin: Alignment.bottomCenter,
      end: Alignment.topCenter,
      stops: <double>[0.02, 0.38, 0.56, 0.93],
      transform: GradientRotation(pi / 180 * 157),
    ),
    width: 1,
  );

  static Widget buildPopupContentWithBlur(Widget child) {
    return Container(
      decoration: const BoxDecoration(
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.05),
            offset: Offset(0, 2),
            blurRadius: 20,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(
          Radius.circular(16),
        ),
        child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 36.0, sigmaY: 36.0), child: child),
      ),
    );
  }
}
