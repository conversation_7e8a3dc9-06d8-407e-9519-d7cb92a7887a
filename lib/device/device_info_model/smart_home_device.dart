/*
 * 描述：智控设备模型
 * 作者：songFJ
 * 建立时间: 12/19/22
 */

import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/typeId_parse/device_type.dart';
import 'package:device_utils/typeId_parse/type_id_parse.dart';
import 'package:flutter/foundation.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:smart_home/common/device_filter_util.dart';

import 'smart_home_device_attribute.dart';
import 'smart_home_device_basic_info.dart';
import 'smart_home_device_state.dart';

class SmartHomeDevice {
  SmartHomeDevice(
      {SmartHomeDeviceOnlineState? onlineState,
      UpDeviceOfflineCause? offlineCause,
      int? offlineDays,
      SmartHomeDeviceBasicInfo? basicInfo,
      Map<String, SmartHomeDeviceAttribute>? attributeMap})
      : onlineState = onlineState ?? SmartHomeDeviceOnlineState.offline,
        offlineCause = offlineCause ?? UpDeviceOfflineCause.CAUSE_NONE,
        offlineDays = offlineDays ?? 0,
        basicInfo = basicInfo ?? SmartHomeDeviceBasicInfo(),
        attributeMap = attributeMap ?? <String, SmartHomeDeviceAttribute>{};

  SmartHomeDevice.fromDeviceInfoModel(DeviceInfoModel deviceInfoModel) {
    onlineState = _deviceOnlineState(deviceInfoModel.onlineStateV2);

    final SmartHomeRoomInfo roomInfo = SmartHomeRoomInfo(
        roomId: deviceInfoModel.room.roomId,
        roomName: deviceInfoModel.room.roomName,
        floorId: deviceInfoModel.devFloorId,
        floorName: getFloorName(deviceInfoModel.devFloorName));
    final SmartHomeOwnerInfo ownerInfo = SmartHomeOwnerInfo(
        ownerId: deviceInfoModel.ownerInfo.userId,
        mobile: deviceInfoModel.ownerInfo.mobile);
    basicInfo = SmartHomeDeviceBasicInfo(
      parentId: deviceInfoModel.parentId,
      appTypeCode: deviceInfoModel.apptypeCode,
      appTypeName: deviceInfoModel.apptypeName,
      imageAddress: deviceInfoModel.imageAddr1,
      cardPageIcon: deviceInfoModel.cardPageIcon,
      cardPageImg: deviceInfoModel.cardPageImg,
      communicationMode: deviceInfoModel.comunicationMode,
      deviceId: deviceInfoModel.deviceId,
      deviceName: deviceInfoModel.deviceName,
      typeId: deviceInfoModel.wifiType,
      bigClass: TypeIdParse.firstTypeCode(deviceInfoModel.wifiType),
      middleClass: TypeIdParse.secondTypeCode(deviceInfoModel.wifiType),
      model: deviceInfoModel.model,
      prodNo: deviceInfoModel.prodNo,
      deviceRole: deviceInfoModel.deviceRole,
      bindTime: deviceInfoModel.bindTime,
      noKeepAlive: deviceInfoModel.noKeepAlive,
      isRebind: deviceInfoModel.isRebind,
      netType: deviceInfoModel.netType,
      ownerId: deviceInfoModel.ownerId,
      ucUserId: deviceInfoModel.ownerInfo.ucUserId,
      deviceGroupType: deviceInfoModel.deviceGroupType,
      configType: deviceInfoModel.configType,
      roomInfo: roomInfo,
      ownerInfo: ownerInfo,
      categoryGrouping: deviceInfoModel.categoryGrouping,
      twoGroupingName: deviceInfoModel.twoGroupingName,
      brand: deviceInfoModel.brand,
      cardSort: deviceInfoModel.cardSort,
      cardStatus: deviceInfoModel.cardStatus,
      aggregationParentId: deviceInfoModel.aggregationParentId,
      isSharedDevice: deviceInfoModel.isSharedDevice,
      isSupportShare: deviceInfoModel.isSupportShare,
      attachmentSortCode: deviceInfoModel.attachmentSortCode,
      accessType: deviceInfoModel.accessType,
    );
  }

  final String _onlineReady = 'ONLINE_READY';
  final String _onlineNotReady = 'ONLINE_NOT_READY';

  SmartHomeDeviceOnlineState onlineState = SmartHomeDeviceOnlineState.offline;
  List<Caution> engineCautions = <Caution>[];
  List<UpDeviceCaution> cautions = <UpDeviceCaution>[];
  UpDeviceOfflineCause offlineCause = UpDeviceOfflineCause.CAUSE_NONE;
  int offlineDays = 0;
  SmartHomeDeviceBasicInfo basicInfo = SmartHomeDeviceBasicInfo();
  Map<String, SmartHomeDeviceAttribute> attributeMap =
      <String, SmartHomeDeviceAttribute>{};

  SmartHomeDeviceOnlineState _deviceOnlineState(String onlineStateV2) {
    SmartHomeDeviceOnlineState onlineState = SmartHomeDeviceOnlineState.offline;
    if (onlineStateV2 == _onlineReady) {
      onlineState = SmartHomeDeviceOnlineState.onlineReady;
    } else if (onlineStateV2 == _onlineNotReady) {
      onlineState = SmartHomeDeviceOnlineState.onlineNotReady;
    }
    return onlineState;
  }

  void updateDeviceInfo(DeviceInfoModel deviceInfoModel) {
    onlineState = _deviceOnlineState(deviceInfoModel.onlineStateV2);
    final SmartHomeRoomInfo roomInfo = SmartHomeRoomInfo(
        roomId: deviceInfoModel.room.roomId,
        roomName: deviceInfoModel.room.roomName,
        floorId: deviceInfoModel.devFloorId,
        floorName: getFloorName(deviceInfoModel.devFloorName));
    final SmartHomeOwnerInfo ownerInfo = SmartHomeOwnerInfo(
        ownerId: deviceInfoModel.ownerInfo.userId,
        mobile: deviceInfoModel.ownerInfo.mobile);

    basicInfo = SmartHomeDeviceBasicInfo(
      parentId: deviceInfoModel.parentId,
      appTypeCode: deviceInfoModel.apptypeCode,
      appTypeName: deviceInfoModel.apptypeName,
      imageAddress: deviceInfoModel.imageAddr1,
      cardPageIcon: deviceInfoModel.cardPageIcon,
      cardPageImg: deviceInfoModel.cardPageImg,
      communicationMode: deviceInfoModel.comunicationMode,
      deviceId: deviceInfoModel.deviceId,
      deviceName: deviceInfoModel.deviceName,
      typeId: deviceInfoModel.wifiType,
      bigClass: TypeIdParse.firstTypeCode(deviceInfoModel.wifiType),
      middleClass: TypeIdParse.secondTypeCode(deviceInfoModel.wifiType),
      model: deviceInfoModel.model,
      prodNo: deviceInfoModel.prodNo,
      deviceRole: deviceInfoModel.deviceRole,
      bindTime: deviceInfoModel.bindTime,
      noKeepAlive: deviceInfoModel.noKeepAlive,
      isRebind: deviceInfoModel.isRebind,
      netType: deviceInfoModel.netType,
      ownerId: deviceInfoModel.ownerId,
      ucUserId: deviceInfoModel.ownerInfo.ucUserId,
      deviceGroupType: deviceInfoModel.deviceGroupType,
      configType: deviceInfoModel.configType,
      roomInfo: roomInfo,
      ownerInfo: ownerInfo,
      categoryGrouping: deviceInfoModel.categoryGrouping,
      twoGroupingName: deviceInfoModel.twoGroupingName,
      brand: deviceInfoModel.brand,
      configState: basicInfo.configState,
      faultInformationStateCode: basicInfo.faultInformationStateCode,
      onlyConfigState: basicInfo.onlyConfigState,
      cardSort: deviceInfoModel.cardSort,
      cardStatus: deviceInfoModel.cardStatus,
      aggregationParentId: deviceInfoModel.aggregationParentId,
      deviceGroupId: deviceInfoModel.deviceGroupId,
      isSharedDevice: deviceInfoModel.isSharedDevice,
      isSupportShare: deviceInfoModel.isSupportShare,
      attachmentSortCode: deviceInfoModel.attachmentSortCode,
      accessType: deviceInfoModel.accessType,
    );
  }

  void updateAttribute(DeviceAttributeModel value) {
    if (value.configState == ConfigState.LOADING) {
      basicInfo.configState = SmartHomeDeviceConfigState.loading;
      return;
    } else if (value.configState == ConfigState.UNSUPPORTED) {
      basicInfo.configState = SmartHomeDeviceConfigState.unsupported;
    } else if (value.configState == ConfigState.SUPPORTED) {
      basicInfo.configState = SmartHomeDeviceConfigState.supported;
    } else if (value.configState == ConfigState.NATIVE) {
      basicInfo.configState = SmartHomeDeviceConfigState.native;
    } else if (value.configState == ConfigState.NOT_NETWORK) {
      basicInfo.configState = SmartHomeDeviceConfigState.notNetwork;
    }

    //待配网字段,默认不支持配网
    basicInfo.onlyConfigState = value.onlyConfigState;
    basicInfo.faultInformationStateCode = value.faultInformationStateCode;

    onlineState = _deviceOnlineState(value.onlineStateV2);

    offlineCause = value.offlineCause;
    offlineDays = value.offlineDays;
    final Map<String, SmartHomeDeviceAttribute> scDeviceAttributeMap =
        <String, SmartHomeDeviceAttribute>{};

    if (DeviceType.washDevice(basicInfo.typeId)) {
      for (final UpDeviceAttribute element in value.attributes) {
        final SmartHomeDeviceAttribute scDeviceAttribute =
            SmartHomeDeviceAttribute(name: element.name, value: element.value);
        scDeviceAttributeMap[element.name] = scDeviceAttribute;
      }
    } else if (value.configState == ConfigState.SUPPORTED) {
      for (final Attribute element in value.engineAttributes) {
        final bool writable = element.writable;
        final SmartHomeDeviceAttributeValueRangeType type =
            scDeviceAttributeValueRangeType(element.valueRange.type);
        final List<SmartHomeDataItem> dataList = <SmartHomeDataItem>[];
        for (final DataItem dataItem in element.valueRange.dataList) {
          final SmartHomeDataItem scDataItem = SmartHomeDataItem(
              data: dataItem.data, desc: dataItem.desc, code: dataItem.code);
          dataList.add(scDataItem);
        }
        final SmartHomeTransform scTransform = SmartHomeTransform(
            k: element.valueRange.dataStep.transform.k,
            c: element.valueRange.dataStep.transform.c);
        final SmartHomeDataStep scDataStep = SmartHomeDataStep(
            dataType: element.valueRange.dataStep.dataType,
            step: element.valueRange.dataStep.step,
            minValue: element.valueRange.dataStep.minValue,
            maxValue: element.valueRange.dataStep.maxValue,
            transform: scTransform);
        final SmartHomeDeviceAttributeValueRange valueRange =
            SmartHomeDeviceAttributeValueRange(
                type: type, dataList: dataList, dataStep: scDataStep);

        final SmartHomeDeviceAttribute scDeviceAttribute =
            SmartHomeDeviceAttribute(
                name: element.name,
                value: element.value,
                writable: writable,
                valueRange: valueRange);
        scDeviceAttributeMap[element.name] = scDeviceAttribute;
      }
    }

    attributeMap.addAll(scDeviceAttributeMap);

    engineCautions = value.engineCautions;
    cautions = value.cautions;
  }

  bool isNeedMarked() {
    return true;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmartHomeDevice &&
          runtimeType == other.runtimeType &&
          _onlineReady == other._onlineReady &&
          _onlineNotReady == other._onlineNotReady &&
          onlineState == other.onlineState &&
          offlineCause == other.offlineCause &&
          listEquals(engineCautions, other.engineCautions) &&
          listEquals(cautions, other.cautions) &&
          offlineDays == other.offlineDays &&
          basicInfo == other.basicInfo &&
          mapEquals(attributeMap, other.attributeMap);

  @override
  int get hashCode =>
      _onlineReady.hashCode ^
      _onlineNotReady.hashCode ^
      onlineState.hashCode ^
      listHashCode(engineCautions) ^
      listHashCode(cautions) ^
      offlineCause.hashCode ^
      offlineDays.hashCode ^
      basicInfo.hashCode ^
      mapHashCode(attributeMap);

  @override
  String toString() {
    return 'SmartHomeDevice{_onlineReady: $_onlineReady, _onlineNotReady: $_onlineNotReady, onlineState: $onlineState, engineCautions: $engineCautions, cautions: $cautions, offlineCause: $offlineCause, offlineDays: $offlineDays, basicInfo: $basicInfo, attributeMap: $attributeMap}';
  }
}
