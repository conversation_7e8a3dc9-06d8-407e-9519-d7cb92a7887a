import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';

/// 更新全屋室外天气状态的Action
class UpdateWholeHouseOutdoorWeatherAction {
  /// 天气模型数据
  final OutdoorWeatherModel weatherModel;

  /// 构造函数
  const UpdateWholeHouseOutdoorWeatherAction(this.weatherModel);

  @override
  String toString() {
    return 'UpdateWholeHouseOutdoorWeatherAction{weatherModel: $weatherModel}';
  }
}

/// 清空全屋室外天气状态的Action
class ClearWholeHouseOutdoorWeatherAction {
  /// 构造函数
  const ClearWholeHouseOutdoorWeatherAction();

  @override
  String toString() {
    return 'ClearWholeHouseOutdoorWeatherAction';
  }
}
