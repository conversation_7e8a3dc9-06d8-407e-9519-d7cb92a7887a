import 'package:smart_home/device/aggregation/aggregation_detail/view_model/aggregation_room_select_view_model.dart';
import '../edit_presenter/edit_presenter_manager.dart';

class EditBaseAction {}

/// 长按设备卡片（全屋、快捷、设备、摄像头）进入编辑
class EnterEditStateByCardAction extends EditBaseAction {
  final String id;

  EnterEditStateByCardAction({required this.id});
}

class EnterEditStateBySceneCardAction extends EditBaseAction {
  final String id;

  EnterEditStateBySceneCardAction({required this.id});
}

class UpdateDeviceCardSizeAction extends EditBaseAction {
  UpdateDeviceCardSizeAction();
}

/// 卡片（设备/聚合）选择或取消选择
class UpdateDeviceCardSelectedStateAction extends EditBaseAction {
  final String id;

  UpdateDeviceCardSelectedStateAction({required this.id});
}

/// 退出编辑
class ExitEditStateAction extends EditBaseAction {
  final List<String>? allCardSortList;
  ExitEditStateAction({this.allCardSortList});
}

/// 重置判断网络恢复后是否需要保存编辑数据的标识
class ResetIsNeedSaveEditedCardListDataAction extends EditBaseAction {
  ResetIsNeedSaveEditedCardListDataAction();
}

/// 拖动调整大小状态
class DragResizeAction extends EditBaseAction {
  final bool dragging;
  DragResizeAction({required this.dragging});
}

/// 卡片（场景）选择或取消选择
class UpdateSceneCardSelectedStatusAction extends EditBaseAction {
  final String id;

  UpdateSceneCardSelectedStatusAction({required this.id});
}

/// 更新编辑功能按钮列表
class UpdateEditBtnListAction extends EditBaseAction {
  final EditPageType pageType;

  UpdateEditBtnListAction({required this.pageType});
}

/// 点击全选、全不选
class UpdateRoomSelectAllStatusAction extends EditBaseAction {
  final AggregationRoomSelectViewModel aggregationRoomSelectVM;

  UpdateRoomSelectAllStatusAction({required this.aggregationRoomSelectVM});
}
