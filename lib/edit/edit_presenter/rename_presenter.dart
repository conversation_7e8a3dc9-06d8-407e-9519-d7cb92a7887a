import 'dart:io';

import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../device/resize_device_card/resize_base_model.dart';
import '../../store/smart_home_store.dart';
import '../edit_card_base_info_model.dart';
import '../store/edit_action.dart';
import '../util/edit_common_dialog.dart';
import '../util/edit_constant.dart';
import '../util/edit_manager.dart';
import 'edit_presenter_manager.dart';

class RenamePresenter {
  static EditDeviceCardInfo editDevice = EditDeviceCardInfo(
    id: '',
    ownUserId: '',
    type: DeviceCardType.middleCard,
    supportLargeCard: false,
  );
  static FocusNode? _searchFocus;
  static final TextEditingController _textController = TextEditingController();
  static void Function(void Function()) _stateSetter =
      () {} as void Function(void Function() p1);
  static String editDeviceName = ''; // 编辑的设备名称
  static String errorText = EditConstant.renameTips; // 校验提示

  static void showDialog(BuildContext context) {
    errorText = EditConstant.renameTips;
    _setEditDevice();
    EditCommonDialog.show(
      context: context,
      title: EditConstant.renameTitle,
      actionConfig: ActionConfig.horizontal(
        <Widget>[
          ButtonFill(
            invert: false,
            type: ButtonType.secondary,
            text: '取消',
            callback: () {
              SmartHomeEditManager.closeEditDialog();
            },
          ),
          ButtonFill(
            text: '确定',
            callback: () {
              if (editDeviceName.trim().isEmpty) {
                _stateSetter(() {
                  errorText = EditConstant.renameError70014Text;
                });
              } else if (editDeviceName.length > 20) {
                _stateSetter(() {
                  errorText = EditConstant.renameOverLimit;
                });
              } else if (editDevice.deviceName == editDeviceName) {
                SmartHomeEditManager.closeEditDialog();
              } else {
                EditPresenterManager.onClickDone(
                  saveHomeCardCallback: ({bool? isSuccess}) {
                    _handleRename();
                  },
                  isNeedCloseDialog: false,
                  isNeedExitEdit: false,
                );
              }
            },
          ),
        ],
      ),
      contentWidget: _buildEditWidget(),
    );
  }

  static void _setEditDevice() {
    editDevice = smartHomeStore.state.editState.selectedCardsList[0];
    editDeviceName = editDevice.deviceName ?? '';
    _textController.value = TextEditingValue(
      text: editDeviceName,
    );
  }

  static Widget _buildEditWidget() {
    return StatefulBuilder(
      builder:
          (BuildContext context, void Function(void Function()) stateSetter) {
        _stateSetter = stateSetter;
        return Container(
          margin: const EdgeInsets.only(top: 8, bottom: 4),
          child: Column(
            children: <Widget>[
              Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                        color: errorText == EditConstant.renameTips
                            ? AppSemanticColors.component.secondary.emphasize
                            : AppSemanticColors.item.warn.primary),
                  ),
                  // padding: const EdgeInsets.all(16),
                  height: 54,
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 14),
                          child: TextField(
                            autocorrect: false,
                            autofocus: true,
                            style: TextStyle(
                              fontSize: 16,
                              color: AppSemanticColors.item.primary,
                            ),
                            cursorWidth: 1,
                            cursorHeight: 18,
                            focusNode: _searchFocus,
                            controller: _textController,
                            decoration: InputDecoration(
                              isDense: true,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5),
                                borderSide: BorderSide.none,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5),
                                borderSide: BorderSide.none,
                              ),
                              hintText: EditConstant.renamePlaceHolder,
                              hintStyle: const TextStyle(
                                  fontSize: 16, color: Color(0xFF999999)),
                              contentPadding:
                                  const EdgeInsets.symmetric(vertical: 6),
                            ),
                            onChanged: (String v) {
                              stateSetter(() {
                                editDeviceName = v;
                              });
                            },
                          ),
                        ),
                      ),
                      Visibility(
                        visible: editDeviceName != '',
                        child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            stateSetter(() {
                              editDeviceName = '';
                              errorText = EditConstant.renameTips;
                            });
                            _textController.clear();
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: Image.asset(
                              'assets/images/edit/input_close.webp',
                              package: 'smart_home',
                              width: 20,
                              height: 20,
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
              Visibility(
                  visible: errorText != '',
                  child: Container(
                    padding: const EdgeInsets.only(top: 8, left: 4, right: 4),
                    width: double.infinity,
                    child: Text(
                      errorText,
                      style: TextStyle(
                        fontSize: 12,
                        color: errorText == EditConstant.renameTips
                            ? AppSemanticColors.item.secondary
                            : AppSemanticColors.item.warn.primary,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ))
            ],
          ),
        );
      },
    );
  }

  static Future<void> _handleRename() async {
    String errText = '';
    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'RenamePresenter _handleRename request deviceId = '
            '${editDevice.id} deviceName = $editDeviceName');
    Family.updateDeviceName(editDevice.id, editDeviceName, '2', false)
        .then((dynamic res) {
      ToastHelper.showToast(EditConstant.renameSuccessText);
      SmartHomeEditManager.closeEditDialog();
      smartHomeStore.dispatch(ExitEditStateAction());
      DevLogger.info(
          tag: SmartHomeEditManager.tag,
          msg: 'RenamePresenter _handleRename success response = $res');
    }).catchError((dynamic errInfo) {
      final PlatformException error = errInfo as PlatformException;
      if (error.code == EditConstant.renameErrorB00007) {
        errText = EditConstant.renameErrorB00007Text;
      } else if (error.code == EditConstant.renameErrorB00009) {
        errText = EditConstant.renameErrorB00009Text;
      } else if (error.code == EditConstant.renameErrorB00011) {
        errText = EditConstant.renameErrorB00011Text;
      } else if (error.code == EditConstant.renameErrorB00010) {
        errText = EditConstant.renameErrorB00010Text;
      } else if (error.code == EditConstant.renameErrorB00014) {
        errText = EditConstant.renameErrorB00014Text;
      } else {
        ToastHelper.showToast(EditConstant.renameFail);
        SmartHomeEditManager.closeEditDialog();
        smartHomeStore.dispatch(ExitEditStateAction());
      }
      if (errText != '') {
        _stateSetter(() {
          errorText = errText;
        });
      }
      DevLogger.error(
          tag: SmartHomeEditManager.tag,
          msg: 'RenamePresenter _handleRename fail response = ${error.code}');
    });
  }
}

class RenameResponseErrorModel {
  String code;

  RenameResponseErrorModel({required this.code});
}
