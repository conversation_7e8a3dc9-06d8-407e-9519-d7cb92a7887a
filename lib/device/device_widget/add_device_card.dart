import 'package:flutter/material.dart';
import 'package:smart_home/common/smart_home_text_widget.dart';
import 'package:smart_home/device/device_view_model/add_device_card_view_model.dart';
import 'package:smart_home/store/smart_home_store.dart';

class AddDeviceCard extends StatelessWidget {
  final AddDeviceCardViewModel viewModel;

  const AddDeviceCard({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: !smartHomeStore.state.isEditState,
      child: GestureDetector(
        onTap: () {
          if (!smartHomeStore.state.isEditState) {
            viewModel.cardClick();
          }
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.all(
            Radius.circular(22),
          ),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
                  child: SmartHomeText(
                      text: viewModel.title,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      height: 1,
                      color: const Color(0xff111111)),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: Image.asset(
                    viewModel.buttonIconUrl,
                    width: 32,
                    height: 32,
                    package: 'smart_home',
                  ),
                ),
                const SizedBox(height: 8),
                Align(
                  child: SmartHomeText(
                    text: viewModel.buttonText,
                    fontSize: 12,
                    height: 1,
                    color: const Color(0xff999999),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
