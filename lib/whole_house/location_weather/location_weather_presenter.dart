import 'package:device_utils/log/log.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/location_weather/actions/area_actions.dart';
import 'package:smart_home/whole_house/location_weather/actions/outdoor_weather_actions.dart';
import 'package:smart_home/whole_house/location_weather/models/area_model.dart';
import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';
import 'package:smart_home/whole_house/location_weather/services/location/location_model.dart';

import '../env_weather_coordinator/env_device_weather_coordinator.dart';
import 'services/location/real_location_service.dart';
import 'services/weather/real_weather_service.dart';

/// 位置天气Presenter
///
/// 负责获取位置和天气数据并更新到Store
class LocationWeatherPresenter {
  // 单例实例
  static final LocationWeatherPresenter _instance = LocationWeatherPresenter._internal();

  // 工厂构造函数
  factory LocationWeatherPresenter() => _instance;

  // 私有构造函数
  LocationWeatherPresenter._internal();

  /// 获取位置和天气数据
  Future<void> getLocationAndWeatherData() async {
    // 查询最新位置数据
    final LocationModel? location = await RealLocationService().getLocation();
    if (location != null) {
      DevLogger.info(tag: SmartHomeConstant.package, msg: 'location: $location');
      smartHomeStore.dispatch(UpdateWholeHouseAreaAction(AreaModel(
        areaName: location.areaName,
        areaCode: location.areaCode,
      )));
    }

    // 查询最新天气数据
    final OutdoorWeatherModel? outdoorWeatherModel = await RealWeatherService().getWholeHouseWeather(
      areaId: location?.areaCode,
      longitude: location?.coordinate.longitude.toString(),
      latitude: location?.coordinate.latitude.toString(),
    );
    if (outdoorWeatherModel != null) {
      smartHomeStore.dispatch(UpdateWholeHouseOutdoorWeatherAction(outdoorWeatherModel));
    
      if (location == null) {
        DevLogger.debug(tag: SmartHomeConstant.package, msg: 'outdoorWeatherModel.area server ip location: $outdoorWeatherModel');
        final String areaName = _getAreaNameFromWeatherModel(outdoorWeatherModel);
        smartHomeStore.dispatch(UpdateWholeHouseAreaAction(AreaModel(
          areaName: areaName,
          areaCode:  outdoorWeatherModel.area.areaId,
        )));
      }
    }
  }

  /// 获取位置和天气数据(使用协调器)
  Future<void> getLocationAndWeatherDataWithCoordinator(
      EnvDeviceWeatherCoordinator coordinator) async {
    // 查询最新位置数据
    final LocationModel? location = await RealLocationService().getLocation();
    if (location != null) {
      coordinator.setLocationData(location);
    }

    // 查询最新天气数据
    final OutdoorWeatherModel? outdoorWeatherModel =
        await RealWeatherService().getWholeHouseWeather(
      areaId: location?.areaCode,
      longitude: location?.coordinate.longitude.toString(),
      latitude: location?.coordinate.latitude.toString(),
    );
    if (outdoorWeatherModel != null) {
      coordinator.setWeatherData(outdoorWeatherModel);
    }
  }

  /// 更新store里天气和位置信息
  void updateLocationAndWeatherData(
    LocationModel? location,
    OutdoorWeatherModel? outdoorWeatherModel,
  ) {
    if (outdoorWeatherModel != null) {
      smartHomeStore
          .dispatch(UpdateWholeHouseOutdoorWeatherAction(outdoorWeatherModel));

      if (location == null) {
        DevLogger.debug(
            tag: SmartHomeConstant.package,
            msg:
                'outdoorWeatherModel.area server ip location: $outdoorWeatherModel');
        final String areaName =
            _getAreaNameFromWeatherModel(outdoorWeatherModel);
        smartHomeStore.dispatch(UpdateWholeHouseAreaAction(AreaModel(
          areaName: areaName,
          areaCode: outdoorWeatherModel.area.areaId,
        )));
      } else {
        DevLogger.debug(
            tag: SmartHomeConstant.package, msg: 'location: $location');
        smartHomeStore.dispatch(UpdateWholeHouseAreaAction(AreaModel(
          areaName: location.areaName,
          areaCode: location.areaCode,
        )));
      }
    }
  }

  /// 优先级：区/县 > 城市 > 省份 > 国家
  String _getAreaNameFromWeatherModel(OutdoorWeatherModel weatherModel) {
    if (weatherModel.area.district.isNotEmpty) {
      return weatherModel.area.district;
    }
    
    if (weatherModel.area.city.isNotEmpty) {
      return weatherModel.area.city;
    }
    
    if (weatherModel.area.province.isNotEmpty) {
      return weatherModel.area.province;
    }
    
    if (weatherModel.area.country.isNotEmpty) {
      return weatherModel.area.country;
    }
    
    return '';
  }
}
