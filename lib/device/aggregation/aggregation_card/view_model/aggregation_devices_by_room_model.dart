import 'package:connectivity/connectivity.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:upsystem/upsystem.dart';

import '../../../device_info_model/smart_home_device_basic_info.dart';
import '../../aggregation_detail/widgets/aggregation_detail_list.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';

/// Aggregation View Model By Room
class AggregationDevicesByRoomViewModel {
  /// 房间信息
  SmartHomeRoomInfo? roomInfo;

  /// 对应房间下的设备列表
  List<String> deviceList = <String>[];

  /// 打开数量
  int openCount = 0;

  /// 在线数量
  int onlineCount = 0;

  /// 空间级别设备列表状态：未知，部分开关，全部打开，全部关闭，全部离线
  DeviceStateForAggregation deviceStateForAggregation =
      DeviceStateForAggregation.UNKNOWN;

  /// 房间开启
  bool isRoomOpen = false;

  /// keyModelTip 信息
  Map<String, String> keyModelTips = <String, String>{};

  AggregationDevicesByRoomViewModel({this.roomInfo});

  String get roomDeviceAllOpenTip => '所有设备已开启';
  String get roomDeviceAllCloseTip => '所有设备已开启';

  void init() {}

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationDevicesByRoomViewModel &&
          runtimeType == other.runtimeType &&
          roomInfo == other.roomInfo &&
          openCount == other.openCount &&
          deviceStateForAggregation == other.deviceStateForAggregation &&
          isRoomOpen == other.isRoomOpen &&
          mapEquals(keyModelTips, other.keyModelTips);

  @override
  int get hashCode =>
      roomInfo.hashCode ^
      openCount.hashCode ^
      deviceStateForAggregation.hashCode ^
      isRoomOpen.hashCode ^
      mapHashCode(keyModelTips);

  void turnOnAllRoomDevices(
      BuildContext? c, String deviceId, Function() callback) {
    UpSystem.impactFeedBack();
    DevLogger.debug(tag: 'SmartHomeDevice', msg: 'turnOnAllRoomDevices start.');
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      } else {
        if (DeviceStateForAggregation.ALL_OFFLINE ==
            deviceStateForAggregation) {
          ToastHelper.showToast('相关设备已离线，请检查后再试');
        } else if (DeviceStateForAggregation.ALL_OPEN ==
            deviceStateForAggregation) {
          ToastHelper.showToast(roomDeviceAllOpenTip);
        } else if (onlineCount > 0) {
          callback();
          // 处理开关命令下发
          emitPowerCommand(true);
          debugPrint('room emitPowerCommand true');
        } else {
          DevLogger.debug(
              tag: 'SmartHomeDevice', msg: 'turnOnAllRoomDevices not match.');
        }
      }
    });
  }

  void emitPowerCommand(bool powerOn) {}

  void turnOffAllRoomDevices(
      BuildContext? c, String deviceId, Function() callback) {
    UpSystem.impactFeedBack();
    DevLogger.debug(
        tag: 'SmartHomeDevice', msg: 'turnOffAllRoomDevices start.');
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
      } else {
        if (smartHomeStore.state.isEditState) {
          return;
        } else if (DeviceStateForAggregation.ALL_OFFLINE ==
            deviceStateForAggregation) {
          ToastHelper.showToast('相关设备已离线，请检查后再试');
        } else if (DeviceStateForAggregation.ALL_CLOSE ==
            deviceStateForAggregation) {
          ToastHelper.showToast(roomDeviceAllCloseTip);
        } else if (onlineCount > 0) {
          callback();
          // 处理开关命令下发
          emitPowerCommand(false);
          debugPrint('room emitPowerCommand false');
        } else {
          DevLogger.debug(
              tag: 'SmartHomeDevice', msg: 'turnOffAllRoomDevices not match.');
        }
      }
    });
  }
}

enum DeviceStateForAggregation {
  UNKNOWN,
  PART_OPEN,
  ALL_OPEN,
  ALL_CLOSE,
  ALL_OFFLINE,
}
