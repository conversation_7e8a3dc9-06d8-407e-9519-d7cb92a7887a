/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2024/6/26
 */

import 'dart:async';
import 'dart:io';

import 'package:connectivity/connectivity.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:library_widgets/common/alert_common_dialog.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:plugin_device/model/device_result.dart';
import 'package:plugin_device/plugin/logicengine_plugin.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_util.dart';
import 'package:smart_home/device/component/view_model/fixed_ble_view_model.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/component_view_model/device_edit_component_view_model.dart';
import 'package:smart_home/device/device_alarm_info.dart';
import 'package:smart_home/device/device_info_model/device_card_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/factory/air_condition_support_device.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../edit/store/edit_action.dart';
import '../../response_time_tracker/response_time_tracker.dart';
import '../../response_time_tracker/response_time_tracker_model.dart';
import '../aggregation/agg_store/aggregation_device_reducer_support.dart';
import '../aggregation/aggregation_card/util/aggregation_device_util.dart';
import '../component_view_model/popup_component_view_model.dart';
import '../component_view_model/power_on_off_btn_model/power_button_animation_view_model.dart';
import '../component_widget/dynamic_popup_bottom_sheet.dart';
import '../device_info_model/smart_home_device_basic_info.dart';
import '../resize_device_card/resize_base_model.dart';
import '../store/device_action.dart';
import 'card_base_view_model.dart';

class DeviceCardViewModel extends CardBaseViewModel {
  DeviceCardViewModel({required this.device})
      : super(ValueKey<String>(device.basicInfo.deviceId),
            convertFromCardStatus(device.basicInfo.cardStatus));

  Map<String, DeviceCardViewModel>? childViewModelMap;

  Set<String> categorySet = <String>{};

  @override
  DeviceCardType get deviceCardType {
    if (size == DeviceCardType.largeCard && !supportLargeCard) {
      return DeviceCardType.middleCard;
    }
    return size;
  }

  final Dialogs _dialogs = Dialogs();

  // 设备基础信息+属性信息

  SmartHomeDevice device;

  String get deviceId => device.basicInfo.deviceId;

  String get appTypeName => device.basicInfo.appTypeName;

  String get prodNo => device.basicInfo.prodNo;

  // 设备运行中状态
  bool get runningMode => false;

  // 设备卡片图标
  String get deviceIcon => device.basicInfo.cardPageImg;

  bool get isDeviceIconAsset => false;

  // 快捷卡片图标
  String get quickIcon => device.basicInfo.cardPageIcon;

  // 设备名称
  String get deviceName => device.basicInfo.deviceName;

  // 楼层
  String get floor => device.basicInfo.roomInfo.floorName;

  // 房间
  String get room => isSharedDevice
      ? SmartHomeConstant.shareDeviceTabText
      : device.basicInfo.roomInfo.roomName;

  String get communicationMode => device.basicInfo.communicationMode;

  bool get deviceRole3 => device.basicInfo.deviceRole == '3';

  bool get engineDevice =>
      device.basicInfo.configState == SmartHomeDeviceConfigState.supported;

  bool get bleDevice {
    final String modeType = communicationMode.toLowerCase();
    return modeType == 'ble' ||
        modeType == 'blemesh' ||
        modeType == 'blebroadcast';
  }

  SmartHomeDeviceAttribute? getDeviceAttribute(String key) {
    return device.attributeMap[key];
  }

  // 获取指定的属性值
  String getDeviceCardAttrValueByName(String key) {
    return getDeviceAttribute(key)?.value ?? '';
  }

  // 卡片是否隐藏
  bool get hidden => shouldDeviceHideForAgg(device);

  // 是否是聚合卡片
  bool get isAggregation => isDeviceAggregation(device.basicInfo.deviceId);

  int get deviceCount => 0;

  // 编辑模式状态区文案
  String get editStatus => isAggregation
      ? '$deviceCount${AggregationSettingConstant.deviceSelectableSuffix}'
      : '';

  /// 大卡片状态展示
  String get unionLargeCardStatus {
    final String? abnormalState = _abnormalState;
    if (abnormalState != null) {
      return abnormalState;
    }

    return largeCardStatus;
  }

  String? get _abnormalState {
    if (modelEmpty || loading) {
      return '';
    }

    if (deviceOffline) {
      return offlineInfo;
    }

    if (masterWashDeviceStatus is String) {
      return masterWashDeviceStatus!;
    }
    return null;
  }

  /// 中卡片状态展示
  String get unionMiddleCardStatus {
    final String? abnormalState = _abnormalState;
    if (abnormalState != null) {
      return abnormalState;
    }

    return middleCardStatus;
  }

  /// 中卡片状态区，子类按需重写
  String get middleCardStatus {
    final String stateOne = deviceCardAttributeOne()?.desc ?? '';
    final String stateTwo = deviceCardAttributeTwo()?.desc ?? '';
    if (stateOne.isNotEmpty && stateTwo.isNotEmpty) {
      return '$stateOne $stateTwo';
    } else if (stateOne.isNotEmpty) {
      return stateOne;
    } else if (stateTwo.isNotEmpty) {
      return stateTwo;
    }
    return '';
  }

  /// 小卡片状态展示
  String get unionSmallCardStatus {
    final String? abnormalState = _abnormalState;
    if (abnormalState != null) {
      return abnormalState;
    }

    return smallCardStatus;
  }

  String? get masterWashDeviceStatus => null;

  /// 大卡片状态区，子类按需重写
  String get largeCardStatus {
    final String stateOne = deviceCardAttributeOne()?.desc ?? '';
    final String stateTwo = deviceCardAttributeTwo()?.desc ?? '';
    if (stateOne.isNotEmpty && stateTwo.isNotEmpty) {
      return '$stateOne $stateTwo';
    } else if (stateOne.isNotEmpty) {
      return stateOne;
    } else if (stateTwo.isNotEmpty) {
      return stateTwo;
    }
    return '';
  }

  /// 小卡片状态区，子类按需重写
  String get smallCardStatus {
    /// 默认展示状态1
    final String stateOne = deviceCardAttributeOne()?.desc ?? '';
    return stateOne;
  }

  // 童锁
  bool get childLockOn {
    if (device.onlineState == SmartHomeDeviceOnlineState.offline) {
      return false;
    }
    if (!supportChildLock()) {
      return false;
    }
    String status = '';
    final List<String> childLickKey = childLockKeys();
    for (final String key in childLickKey) {
      if (device.attributeMap.containsKey(key)) {
        final SmartHomeDeviceAttribute? attribute = device.attributeMap[key];
        status = attribute?.value ?? '';
        return status == trueValue;
      }
    }

    return false;
  }

  /// 业务处理后的告警状态
  bool get unionAlarm {
    if (bleDevice || modelEmpty || loading || deviceOffline) {
      return false;
    }
    return alarm;
  }

  /// 通用型获取设备故障列表方法，合并原有两个方法的功能
  List<T> getCautionsOfType<T>() {
    // 检查是否支持设备告警
    if (!supportDeviceAlarm()) {
      return <T>[];
    }

    /// 根据设备类型和请求类型返回合适的列表
    if (T == Caution && engineDevice) {
      final List<Caution> engineCautions = <Caution>[...device.engineCautions];
      engineCautions.removeWhere(
          (Caution caution) => caution.name == provideAlarmCancelKey());
      return engineCautions.cast<T>();
    } else if (T == UpDeviceCaution && !engineDevice) {
      final List<UpDeviceCaution> cautions = <UpDeviceCaution>[
        ...device.cautions
      ];
      cautions.removeWhere(
          (UpDeviceCaution caution) => caution.name == provideAlarmCancelKey());
      return cautions.cast<T>();
    }

    // 类型不匹配时返回空列表
    return <T>[];
  }

  /// 引擎设备：过滤掉告警取消的故障列表
  List<Caution> get engineCautionsNoCancel => getCautionsOfType<Caution>();

  /// 非引擎设备：过滤掉告警取消的故障列表
  List<UpDeviceCaution> get cautionsNoCancel =>
      getCautionsOfType<UpDeviceCaution>();

  bool get alarm {
    if (!supportDeviceAlarm()) {
      return false;
    }
    if (engineDevice) {
      final List<Caution> engineCautions = <Caution>[...device.engineCautions];

      engineCautions.removeWhere(
          (Caution caution) => caution.name == provideAlarmCancelKey());
      if (engineCautions.isNotEmpty) {
        return true;
      }
      return false;
    } else {
      final List<UpDeviceCaution> cautions = <UpDeviceCaution>[
        ...device.cautions
      ];
      cautions.removeWhere(
          (UpDeviceCaution caution) => caution.name == provideAlarmCancelKey());
      if (cautions.isNotEmpty) {
        return true;
      }
      return false;
    }
  }

  final String trueValue = 'true';
  final String falseValue = 'false';

  /// 待配网
  String? get unConfig {
    final SmartHomeDeviceBasicInfo basicInfo = device.basicInfo;
    if (basicInfo.onlyConfigState != UpDeviceOnlyConfigState.UN_CONFIGURABLE &&
        device.basicInfo.faultInformationStateCode == '1004') {
      return '待配网';
    }
    return null;
  }

  /// Wifi密码错误
  String? get wiFiPassWordError {
    final SmartHomeDeviceBasicInfo basicInfo = device.basicInfo;
    if (basicInfo.onlyConfigState != UpDeviceOnlyConfigState.UN_CONFIGURABLE &&
        device.basicInfo.faultInformationStateCode == '1002') {
      return 'Wi-Fi密码错误';
    }
    return null;
  }

  bool get excludeDevice {
    return smartHomeStore.state.deviceState.excludeDeviceList
            .contains(device.basicInfo.model) ||
        smartHomeStore.state.deviceState.excludeDeviceList
            .contains(device.basicInfo.typeId) ||
        smartHomeStore.state.deviceState.excludeDeviceList.contains(
            '${device.basicInfo.bigClass}#${device.basicInfo.middleClass}') ||
        smartHomeStore.state.deviceState.excludeDeviceList
            .contains(device.basicInfo.bigClass) ||
        AirConditionSupportDevice.excludeAirConditionDevice(device);
  }

  /// 型号缺失
  bool get modelEmpty {
    return device.basicInfo.model.isEmpty;
  }

  bool get isSharedDevice => device.basicInfo.isSharedDevice;

  bool get isSupportShare => device.basicInfo.isSupportShare;

  /// 未知通讯方式
  bool get communicationModeUnknown {
    final String communicationMode = device.basicInfo.communicationMode;
    return communicationMode.isEmpty || communicationMode == 'unknown';
  }

  String? get otherDeviceOfflineInfo => null;

  bool get nonNetDevice => device.basicInfo.netType == 'nonNetDevice';

  bool get deviceOffline =>
      device.onlineState == SmartHomeDeviceOnlineState.offline &&
      device.basicInfo.noKeepAlive != 1 &&
      !nonNetDevice;

  bool get deviceOfflineOrPowerOff => deviceOffline || powerOff;

  String get offlineInfo {
    if (nonNetDevice || bleDevice || device.basicInfo.noKeepAlive == 1) {
      return '';
    }

    if (!deviceOffline) {
      return '';
    }

    final UpDeviceOfflineCause offlineCause = device.offlineCause;
    final int offlineDays = device.offlineDays;
    if (offlineCause == UpDeviceOfflineCause.CAUSE_LOW_POWER) {
      return '关';
    } else if (offlineCause == UpDeviceOfflineCause.CAUSE_WIFI_CLOSE) {
      return 'Wi-Fi关闭';
    } else if (offlineDays > 30 || offlineDays == -1) {
      return '长期离线';
    } else if (otherDeviceOfflineInfo != null) {
      return otherDeviceOfflineInfo!;
    } else {
      return '离线';
    }
  }

  bool get lowPower {
    return device.onlineState == SmartHomeDeviceOnlineState.offline &&
        device.offlineCause == UpDeviceOfflineCause.CAUSE_LOW_POWER;
  }

  bool get lowerPowerOrWifiClose {
    return device.onlineState == SmartHomeDeviceOnlineState.offline &&
        (device.offlineCause == UpDeviceOfflineCause.CAUSE_LOW_POWER ||
            device.offlineCause == UpDeviceOfflineCause.CAUSE_WIFI_CLOSE);
  }

  bool get onlineNotReady {
    return device.onlineState == SmartHomeDeviceOnlineState.onlineNotReady;
  }

  bool get loading {
    return device.basicInfo.configState == SmartHomeDeviceConfigState.loading;
  }

  bool get powerOff {
    return onOffAttribute?.value == falseValue;
  }

  bool get powerOn {
    return onOffAttribute?.value == trueValue;
  }

  SmartHomeDeviceAttribute? get onOffAttribute {
    return device.attributeMap['onOffStatus'];
  }

  ComponentBaseViewModel get editComponentViewModel {
    return DeviceEditComponentViewModel(
      isSelected: isSelected,
      selectedClick: () {
        selectCardClick();
      },
    );
  }

  Future<bool> powerButtonCheckContinue() {
    return checkDeviceState(
        checkPowerOff: false, writable: onOffAttribute?.writable ?? false);
  }

  void selectCardClick() {
    smartHomeStore.dispatch(UpdateDeviceCardSelectedStateAction(id: sortId()));
  }

  /// 业务处理后的右上角主功能按钮展示
  ComponentBaseViewModel? get unionTopRightComponentViewModel {
    if (bleDevice) {
      return FixedBleViewModel();
    }

    if (communicationModeUnknown || modelEmpty || loading || deviceOffline) {
      return null;
    }

    /// 小卡片，不展示主功能按钮
    if (deviceCardType == DeviceCardType.smallCard) {
      return null;
    }
    return topRightComponentViewModel;
  }

  // 正常状态下右上角期望展示的功能
  ComponentBaseViewModel? get topRightComponentViewModel {
    if (supportPowerOnOff()) {
      final SmartHomeDeviceAttribute? attribute = onOffAttribute;
      if (attribute == null) {
        return null;
      }

      final PowerButtonAnimationViewModel viewModel =
          PowerButtonAnimationViewModel(
              isOn: attribute.value == trueValue,
              writable: attribute.writable,
              checkContinue: powerButtonCheckContinue,
              btnClick: (BuildContext? context) {
                if (deviceCardType == DeviceCardType.largeCard) {
                  gioTrack(
                      SmartHomeConstant.deviceCardOnOffGio, <String, dynamic>{
                    'deviceid': deviceId,
                    'devicetype': appTypeName,
                    'status': attribute.value == trueValue ? '关机' : '开机',
                    'devno': prodNo,
                  });
                } else if (deviceCardType == DeviceCardType.middleCard) {
                  gioTrack(
                      SmartHomeConstant.middleCardOnOffGio, <String, dynamic>{
                    'deviceid': deviceId,
                    'devicetype': appTypeName,
                    'status': attribute.value == trueValue ? '关机' : '开机',
                    'devno': prodNo,
                  });
                }

                executePowerOnOff(
                    attribute.value == trueValue, attribute.writable);
              });
      return viewModel;
    }
    return null;
  }

  String _selectedFunctionSetKey = '';

  set selectedFunctionSetKey(String value) {
    _selectedFunctionSetKey = value;
  }

  String get selectedFunctionSetKey {
    if (_selectedFunctionSetKey.isNotEmpty &&
        unionLargeCardFuncMap.containsKey(_selectedFunctionSetKey)) {
      return _selectedFunctionSetKey;
    }
    final List<String> keys = unionLargeCardFuncMap.keys.toList();
    if (keys.isEmpty) {
      return '';
    }

    return keys.first;
  }

  bool manualSelectedFuncSet = false;

  Map<String, LargeDeviceCardFunctionSet> get unionLargeCardFuncMap {
    if (modelEmpty) {
      return <String, LargeDeviceCardFunctionSet>{};
    }
    return largeCardFuncMap ?? <String, LargeDeviceCardFunctionSet>{};
  }

  // 大卡片功能集
  Map<String, LargeDeviceCardFunctionSet>? get largeCardFuncMap => null;

  // 弹出框的功能集
  Map<String, PopupComponentViewModel> popupFuncSet =
      <String, PopupComponentViewModel>{};

  DeviceCardAttribute? deviceCardAttributeOne() {
    return null;
  }

  DeviceCardAttribute? deviceCardAttributeTwo() {
    return null;
  }

  DeviceCardAttribute? deviceCardAttributeThree() {
    return null;
  }

  bool isPowerOutWashDevice() {
    return false;
  }

  void gio({String? cloudProgramName}) {
    gioTrack('MB36982', <String, dynamic>{
      'cloud_program_name': cloudProgramName ?? '',
      'deviceid': deviceId,
      'devicetype': appTypeName,
      'devno': prodNo,
    });
  }

  /// 卡片点击重定向，默认卡片点击进行VDN跳转，如需重定向，重写该方法返回true
  bool cardClickRedirect(BuildContext? context) {
    return false;
  }

  void cardClick(BuildContext? context, {String? jumpUrl}) {
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
        return;
      }
      DevLogger.info(
          tag: SmartHomeConstant.package,
          msg:
              'goToPage <DetailPage>, deviceId: $deviceId,typeId: ${device.basicInfo.typeId}, configState: ${device.basicInfo.configState},model:${device.basicInfo.model}');
      if (device.basicInfo.configState == SmartHomeDeviceConfigState.loading) {
        ToastHelper.showToast(SmartHomeConstant.toastDeviceOnlineNotReadyInfo);
        return;
      }
      if (modelEmpty) {
        gioTrack(SmartHomeConstant.modelEmptyGio, <String, String>{
          'deviceid': device.basicInfo.deviceId,
          'devicetype': appTypeName,
          'cardType':
              deviceCardType == DeviceCardType.middleCard ? '中卡片' : '小卡片',
        });
        goToPageWithDebounce(
            SmartHomeConstant.vdnDeviceDetail + device.basicInfo.deviceId);
        return;
      }

      if (!cardClickRedirect(context)) {
        if (deviceCardType == DeviceCardType.largeCard) {
          gioTrack(SmartHomeConstant.largeCardClickGio, <String, String>{
            'deviceid': deviceId,
            'devicetype': appTypeName
          });
        } else {
          gioTrack(
              SmartHomeConstant.middleAndSmallCardClickGio, <String, String>{
            'deviceid': deviceId,
            'devicetype': appTypeName,
            'cardType':
                deviceCardType == DeviceCardType.middleCard ? '中卡片' : '小卡片'
          });
        }

        String url =
            SmartHomeConstant.vdnDeviceDetail + device.basicInfo.deviceId;
        if (jumpUrl != null && jumpUrl.isNotEmpty) {
          url = jumpUrl;
        }
        goToPageWithDebounce(url);
      }
    });
  }

  void jumpWifiSetConfig(BuildContext? context) {
    gioTrack('MB36989', <String, dynamic>{
      'onlyConfigState': device.basicInfo.onlyConfigState.toString(),
      'faultInformationStateCode': device.basicInfo.faultInformationStateCode,
      'deviceid': deviceId,
      'devicetype': appTypeName,
    });
    if (device.basicInfo.onlyConfigState ==
        UpDeviceOnlyConfigState.CONFIGURABLE) {
      goToPageWithDebounce(
          '${SmartHomeConstant.wifiSettingVdn}${device.basicInfo.deviceId}');
    } else {
      if (context == null) {
        return;
      }
      _dialogs.showSingleBtnDialog(
          context: context,
          title: '温馨提示',
          content: '请将手机靠近设备，并确保手机的蓝牙${Platform.isAndroid ? '及定位' : ''}\n权限已开启',
          callback: () {});
    }
  }

  void alarmClick(BuildContext context) {
    String cardType = '大卡片';
    if (deviceCardType == DeviceCardType.middleCard) {
      cardType = '中卡片';
    } else if (deviceCardType == DeviceCardType.smallCard) {
      cardType = '小卡片';
    }
    gioTrack(
        SmartHomeConstant.alarmGio, <String, String>{'cardType': cardType});

    if (engineDevice) {
      /// 逻辑引擎设备
      final String alarmStrings =
          engineDeviceAlarmStrings(device.engineCautions);
      if (alarmStrings.isNotEmpty) {
        showAlarms(context, alarmStrings);
      }
      DevLogger.info(
          tag: SmartHomeConstant.package,
          msg: 'engineCautions - ${device.engineCautions}');
    } else {
      /// 非逻辑引擎设备
      final String codeStrings = _nonEngineDeviceAlarmNames(device.cautions);
      HttpService.alarmInfo(codeStrings, device.basicInfo.typeId)
          .then((AlarmInfoModel? alarmInfoModel) {
        String alarmStrings = '';
        if (alarmInfoModel != null) {
          final List<DevAlarmInfo> items = alarmInfoModel.data.devAlarmInfo;
          for (int i = 0; i < items.length; i++) {
            alarmStrings =
                '$alarmStrings${i + 1}.${items[i].info} ${i < items.length - 1 ? '\n' : ''}';
          }
        }
        if (alarmStrings.isNotEmpty) {
          showAlarms(context, alarmStrings);
        }
        DevLogger.info(
            tag: SmartHomeConstant.package, msg: 'cautions - $alarmInfoModel');
      });
    }
  }

  void showAlarms(BuildContext context, String alarmStrings) {
    _dialogs.showDoubleBtnDialog(
        context: context,
        title: '故障说明',
        content: alarmStrings,
        confirmText: '一键报修',
        confirmCallback: () {
          _showServiceCallDialog(context);
        });
  }

  void _showServiceCallDialog(BuildContext context) {
    Future<void>.delayed(const Duration(milliseconds: 500), () {
      _dialogs.showDoubleBtnDialog(
          context: context,
          title: '',
          content: '现在拨打海尔售后电话:\n************',
          confirmText: '呼叫',
          confirmCallback: () {
            _launchPhoneCall('************');
          });
    });
  }

  void _launchPhoneCall(String phoneNumber) {
    final String url = 'tel:$phoneNumber';
    launch(url);
  }

  String _nonEngineDeviceAlarmNames(List<UpDeviceCaution> items) {
    String alarms = '';
    if (items.isNotEmpty) {
      for (final UpDeviceCaution caution in items) {
        if (caution.name.isNotEmpty) {
          alarms = '$alarms${caution.name},';
        }
      }
    }
    alarms = alarms.length > 1 ? alarms.substring(0, alarms.length - 1) : '';
    return alarms;
  }

  String engineDeviceAlarmStrings(List<Caution> items) {
    String alarms = '';
    if (items.isNotEmpty) {
      for (int i = 0; i < items.length; i++) {
        alarms =
            '$alarms${i + 1}.${items[i].desc} ${i < items.length - 1 ? '\n' : ''}';
      }
    }
    return alarms;
  }

  bool supportQuickControl() {
    return largeCardFuncMap?.isNotEmpty ?? false;
  }

  /// 是否支持大卡片
  @override
  bool get supportLargeCard {
    return supportQuickControl() && !excludeDevice && !modelEmpty;
  }

  /// 是否支持开关机
  bool supportPowerOnOff() {
    return false;
  }

  /// 是否支持告警
  bool supportDeviceAlarm() {
    return true;
  }

  /// 报警取消 key
  String provideAlarmCancelKey() {
    return SmartHomeConstant.key_alert_cancel_default;
  }

  /// 是否支持童锁
  bool supportChildLock() {
    return true;
  }

  /// 童锁取值key
  List<String> childLockKeys() {
    return <String>['childLockStatus', 'lockStatus'];
  }

  @override
  String sortId() {
    return device.basicInfo.deviceId;
  }

  String get additionalOfflineToast {
    return '';
  }

  /// 通用状态检查
  Future<bool> checkDeviceState({
    bool checkOffline = true,
    bool checkLoading = true,
    String loadingMsg = '',
    bool checkOnlineNotReady = false,
    bool checkPowerOff = true,
    String powerOffMsg = '',
    bool checkChildLock = true,
    bool checkWritable = true,
    bool writable = true,
    String writableMsg = '',
    bool checkAlarm = true,
  }) async {
    bool otherCheck({
      bool checkOffline = true,
      bool checkLoading = true,
      String loadingMsg = '',
      bool checkOnlineNotReady = false,
      bool checkPowerOff = true,
      String powerOffMsg = '',
      bool checkChildLock = true,
      bool checkWritable = true,
      bool writable = true,
      String writableMsg = '',
      bool checkAlarm = true,
    }) {
      /// 离线
      if (checkOffline && deviceOffline) {
        if (additionalOfflineToast.isNotEmpty) {
          ToastHelper.showToast(additionalOfflineToast);
        } else {
          ToastHelper.showToast(SmartHomeConstant.toastDeviceOfflineInfo);
        }
        return false;
      }

      /// 低功耗&wifi关闭
      if (lowerPowerOrWifiClose) {
        return false;
      }

      /// 加载中
      if (checkLoading && loading) {
        if (loadingMsg.isNotEmpty) {
          ToastHelper.showToast(loadingMsg);
        } else {
          ToastHelper.showToast(SmartHomeConstant.toastDeviceLoadingInfo);
        }

        return false;
      }

      /// 在线未就绪
      if (checkOnlineNotReady && onlineNotReady) {
        ToastHelper.showToast(SmartHomeConstant.toastDeviceOnlineNotReadyInfo);
        return false;
      }

      /// 关机
      if (checkPowerOff && powerOff) {
        if (powerOffMsg.isNotEmpty) {
          ToastHelper.showToast(powerOffMsg);
        } else {
          ToastHelper.showToast(SmartHomeConstant.toastDevicePowerOff);
        }

        return false;
      }

      /// 童锁
      if (checkChildLock && childLockOn) {
        ToastHelper.showToast(SmartHomeConstant.toastChildLockOnMsg);
        return false;
      }

      /// 不可写特殊提示
      if (writableMsg.isNotEmpty && checkWritable && !writable) {
        ToastHelper.showToast(writableMsg);
        return false;
      }

      /// 告警&&不可写
      if (checkWritable && !writable && checkAlarm && alarm) {
        ToastHelper.showToast(SmartHomeConstant.toastDeviceAlarmInfo);
        return false;
      }

      if (checkWritable && !writable) {
        ToastHelper.showToast(SmartHomeConstant.toastNotWritable);
        return false;
      }

      return true;
    }

    return Connectivity().checkConnectivity().then((ConnectivityResult result) {
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
        return false;
      }
      return otherCheck(
        checkOffline: checkOffline,
        checkLoading: checkLoading,
        loadingMsg: loadingMsg,
        checkOnlineNotReady: checkOnlineNotReady,
        checkPowerOff: checkPowerOff,
        powerOffMsg: powerOffMsg,
        checkChildLock: checkChildLock,
        checkWritable: checkWritable,
        writable: writable,
        writableMsg: writableMsg,
      );
    }, onError: (_) {
      return otherCheck(
        checkOffline: checkOffline,
        checkLoading: checkLoading,
        loadingMsg: loadingMsg,
        checkOnlineNotReady: checkOnlineNotReady,
        checkPowerOff: checkPowerOff,
        powerOffMsg: powerOffMsg,
        checkChildLock: checkChildLock,
        checkWritable: checkWritable,
        writable: writable,
        writableMsg: writableMsg,
      );
    });
  }

  /// 展示弹窗
  void showPopup(
      BuildContext? context, PopupComponentViewModel? popupComponentViewModel) {
    if (context != null && context.mounted && popupComponentViewModel != null) {
      showDynamicPopupBottomSheet(context);
      smartHomeStore.dispatch(
          UpdatePopupComponentViewModelAction(popupComponentViewModel));
    }
  }

  /// 隐藏弹框
  void hidePopup(BuildContext? context) {
    if (smartHomeStore.state.deviceState.popupComponentViewModel != null) {
      if (context != null) {
        Navigator.pop(context);
        Future<void>.delayed(const Duration(milliseconds: 200), () {
          smartHomeStore.dispatch(UpdatePopupComponentViewModelAction(null));
        });
      }
    }
  }

  Future<void> executePowerOnOff(bool powerOn, bool writable) async {
    final Map<String, String> commands = <String, String>{};
    commands['onOffStatus'] = powerOn ? falseValue : trueValue;
    quickCtrlLECommandWithCmdGio(device.basicInfo.deviceId, commands,
        appTypeCode: device.basicInfo.appTypeCode,
        appTypeName: device.basicInfo.appTypeName,
        model: device.basicInfo.model);
  }

  void quickCtrlLECommand(String deviceId, Map<String, String> cmdMap,
      {void Function(String errMsg)? onErrorCallback}) {
    final List<Command> commands = _assembleCommands(deviceId, cmdMap);

    DevLogger.info(
        tag: 'quickCtrlLECommand',
        msg: 'deviceId: $deviceId, commands: $commands');

    LogicEnginePlugin.operate(deviceId, commands).then((_) {
      //
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: 'quickCtrlLECommand',
          msg: 'deviceId: $deviceId, commands: $commands, err: $err');
      if (onErrorCallback != null) {
        onErrorCallback(SmartHomeConstant.toastFailure);
      }
    });
  }

  void quickCtrlLECommandWithCmdGio(String deviceId, Map<String, String> cmdMap,
      {String appTypeCode = '',
      String appTypeName = '',
      String model = '',
      void Function(String errMsg)? onErrorCallback}) {
    final List<Command> commands = _assembleCommands(deviceId, cmdMap);

    final Map<String, CMDStateModel> cmdStateModelMap =
        <String, CMDStateModel>{};
    ResponseTimeTracker.instance.initOperateStateModel(
        deviceId: deviceId,
        appTypeCode: appTypeCode,
        appTypeName: appTypeName,
        faultInformationStateCode: device.basicInfo.faultInformationStateCode,
        model: model,
        commands: commands,
        cmdStateModelMap: cmdStateModelMap);

    LogicEnginePlugin.operateWithTraceId(deviceId, commands)
        .then((DeviceResult value) {
      if (value.retCode != SmartHomeConstant.logicEngineOperateSuccessCode &&
          onErrorCallback != null) {
        onErrorCallback(SmartHomeConstant.toastFailure);
      }

      ResponseTimeTracker.instance.updateOperateStateModel(
          deviceId: deviceId,
          commands: commands,
          deviceResult: value,
          cmdStateModelMap: cmdStateModelMap);
    }).catchError((dynamic err) {
      if (onErrorCallback != null) {
        onErrorCallback(SmartHomeConstant.toastFailure);
      }
    });
  }

  List<Command> _assembleCommands(String deviceId, Map<String, String> cmdMap) {
    final List<Command> commands = <Command>[];
    cmdMap.forEach((String key, String value) {
      commands.add(
          Command.fromMap(<dynamic, dynamic>{'name': key, 'value': value}));
    });
    return commands;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is DeviceCardViewModel &&
          runtimeType == other.runtimeType &&
          deviceIcon == other.deviceIcon &&
          deviceName == other.deviceName &&
          floor == other.floor &&
          room == other.room &&
          unionLargeCardStatus == other.unionLargeCardStatus &&
          childLockOn == other.childLockOn &&
          alarm == other.alarm &&
          topRightComponentViewModel == other.topRightComponentViewModel &&
          selectedFunctionSetKey == other.selectedFunctionSetKey &&
          manualSelectedFuncSet == other.manualSelectedFuncSet &&
          mapEquals(unionLargeCardFuncMap, other.unionLargeCardFuncMap) &&
          mapEquals(popupFuncSet, other.popupFuncSet);

  @override
  int get hashCode =>
      super.hashCode ^
      deviceIcon.hashCode ^
      deviceName.hashCode ^
      floor.hashCode ^
      room.hashCode ^
      unionLargeCardStatus.hashCode ^
      childLockOn.hashCode ^
      alarm.hashCode ^
      topRightComponentViewModel.hashCode ^
      selectedFunctionSetKey.hashCode ^
      manualSelectedFuncSet.hashCode ^
      mapHashCode(unionLargeCardFuncMap) ^
      mapHashCode(popupFuncSet);
}

enum ComponentAlign {
  /// 有间隔
  interval,

  /// 无间隔
  noInterval,
}

class LargeDeviceCardFunctionSet {
  String name = '';
  ComponentAlign componentAlign = ComponentAlign.interval;
  List<ComponentBaseViewModel> componentViewModelList =
      <ComponentBaseViewModel>[];

  LargeDeviceCardFunctionSet(
      {this.name = '',
      this.componentAlign = ComponentAlign.interval,
      this.componentViewModelList = const <ComponentBaseViewModel>[]});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LargeDeviceCardFunctionSet &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          componentAlign == other.componentAlign &&
          listEquals(componentViewModelList, other.componentViewModelList);

  @override
  int get hashCode =>
      name.hashCode ^
      componentAlign.hashCode ^
      listHashCode(componentViewModelList);

  @override
  String toString() {
    return 'LargeDeviceCardFunctionSet{name: $name, componentAlign: $componentAlign, componentViewModelList: $componentViewModelList}';
  }
}

class WashDeviceLargeDeviceCardFunctionSet extends LargeDeviceCardFunctionSet {
  int unitIndex = 0;

  WashDeviceLargeDeviceCardFunctionSet(
      {required this.unitIndex,
      super.name,
      super.componentAlign,
      super.componentViewModelList});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is WashDeviceLargeDeviceCardFunctionSet &&
          runtimeType == other.runtimeType &&
          unitIndex == other.unitIndex;

  @override
  int get hashCode => super.hashCode ^ unitIndex.hashCode;

  @override
  String toString() {
    return 'WashDeviceLargeDeviceCardFunctionSet{unitIndex: $unitIndex}';
  }
}

DeviceCardType convertFromCardStatus(int cardStatus) {
  switch (cardStatus) {
    case 0:
      return DeviceCardType.smallCard;
    case 1:
      return DeviceCardType.middleCard;
    case 2:
      return DeviceCardType.largeCard;
    default:
      return DeviceCardType.middleCard;
  }
}
