import 'dart:collection';

import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/floor_model.dart';
import 'package:family/room_model.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/device_filter_util.dart';
import 'package:smart_home/device/aggregation/agg_camera/agg_camera_util.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_list_util.dart';
import 'package:smart_home/device/device_view_model/add_device_card_view_model.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/device_view_model/report_for_repair_card_view_model.dart';
import 'package:smart_home/device/device_view_model/song_box_card_view_model.dart';
import 'package:smart_home/device/device_view_model/wash_device_view_model.dart';
import 'package:smart_home/device/fridge_foodnums/fridge_foodnum_presenter.dart';
import 'package:smart_home/device/fridge_foodnums/models/fridge_foodnum_model.dart';
import 'package:smart_home/device/store/camera/camera_reducer.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/device/store/device_state.dart';
import 'package:smart_home/device/store/voice_box/voice_box_reducer_support.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../../smart_home_presenter.dart';
import '../aggregation/agg_nonnet/agg_non_net_view_model.dart';
import '../aggregation/agg_offline/agg_offline_view_model.dart';
import '../aggregation/agg_store/aggregation_device_reducer_support.dart';
import '../aggregation/aggregation_card/util/aggregation_device_util.dart';
import '../device_filter/device_filter_model.dart';
import '../device_info_model/smart_home_device_basic_info.dart';
import '../device_view_model/card_base_view_model.dart';

final Reducer<SmartHomeState> deviceCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdatePopupComponentViewModelAction>(
          _updatePopupComponentViewModel)
      .call,
  TypedReducer<SmartHomeState, SmallCardDragFinishedAction>(
          _smallCardDragFinished)
      .call,
  TypedReducer<SmartHomeState, UpdateExcludeDeviceList>(
          _updateExcludeDeviceList)
      .call,
  TypedReducer<SmartHomeState, UpdateSmallCardListFilteredStateAction>(
          _updateSmallCardListFilteredState)
      .call,
  TypedReducer<SmartHomeState, UpdateDeviceTabIndexAction>(
          _updateDeviceTabIndex)
      .call,
  TypedReducer<SmartHomeState, UpdateDeviceFilterToInitialAction>(
          _updateDeviceFilterToInitial)
      .call,
  TypedReducer<SmartHomeState, DeviceFilterConfirmAction>(
          _updateDeviceFilterToNew)
      .call,
  TypedReducer<SmartHomeState, DeviceMapUpdateAction>(_updateDeviceMapAction)
      .call,
  TypedReducer<SmartHomeState, UpdateDeviceAttributeAction>(
          _updateDeviceAttribute)
      .call,
  TypedReducer<SmartHomeState, UpdateDeviceFilterAction>(_updateDeviceFilter)
      .call,
  TypedReducer<SmartHomeState, WashDeviceDataLoadFinish>(
          _washDeviceDateLoadFinish)
      .call,
  TypedReducer<SmartHomeState, UpdateCustomAttributeAction>(
          _updateCustomAttribute)
      .call,
  TypedReducer<SmartHomeState, FridgeFoodNumsChangedAction>(
          _updateDeviceViewModelByFoodNum)
      .call,
  TypedReducer<SmartHomeState,
              UpdatePurifiedConsumableNameAndLevelAttributeAction>(
          _updatePurifiedConsumableNameAndLevel)
      .call,
  TypedReducer<SmartHomeState, PurifiedConsumeLoadFinish>(
          _updatePurifiedConsumeMap)
      .call,
  TypedReducer<SmartHomeState, UpdatePopupContextAction>(_updatePopupContext)
      .call,
  TypedReducer<SmartHomeState, UpdateSelectedFuncSetKey>(
          _updateSelectedFuncSetKey)
      .call,
  TypedReducer<SmartHomeState, ClearDeviceListAction>(_clearDeviceListAction)
      .call,
  TypedReducer<SmartHomeState, UpdateWashProgram>(_updateWashProgram).call,
  TypedReducer<SmartHomeState, CameraAction>(_updateCameraAction).call,
  TypedReducer<SmartHomeState, UpdateVoiceBoxDeviceAction>(
          _updateVoiceBoxDevice)
      .call,
  TypedReducer<SmartHomeState, ClearDeviceDataAction>(_clearDeviceDataAction)
      .call,
  TypedReducer<SmartHomeState, UpdateDeviceStatusAction>(
          _updateDeviceStatusAction)
      .call,
  TypedReducer<SmartHomeState, UpdateImageRefreshCountAction>(
          _imageRefreshCountReducer)
      .call,
  TypedReducer<SmartHomeState, UpdateScrollableScrollPhysicsAction>(
          _updateScrollPhysicsState)
      .call,
  TypedReducer<SmartHomeState, UpdateAggregationCardAction>(
          _updateAggregationCardReducer)
      .call,
  TypedReducer<SmartHomeState, UpdateAggregationRoomIndexAction>(
          _updateAggregationRoomIndexReducer)
      .call,
  TypedReducer<SmartHomeState, UpdateAggregationRoomOperateAction>(
          _updateAggregationRoomOperatingReducer)
      .call,
  TypedReducer<SmartHomeState, AddAggDeviceAction>(_addAggDeviceReducer).call,
  TypedReducer<SmartHomeState, RemoveAggDeviceAction>(_removeAggDeviceReducer)
      .call,
  TypedReducer<SmartHomeState, UpdateCameraMsgAction>(
          _updateCameraMsgActionReducer)
      .call,
]);

SmartHomeState _updateCameraMsgActionReducer(
    SmartHomeState state, UpdateCameraMsgAction action) {
  updateCameraMsgAction(state, action.data);
  return state;
}

void updateCameraMsgAction(
    SmartHomeState state, Map<String, CameraMsgVM> data) {
  data.forEach((String key, CameraMsgVM value) {
    if (state.deviceState.allCardViewModelMap[key] != null &&
        state.deviceState.allCardViewModelMap[key]
            is CameraDeviceCardViewModel) {
      final CameraDeviceCardViewModel cameraCard = state
          .deviceState.allCardViewModelMap[key]! as CameraDeviceCardViewModel;
      cameraCard.updateCameraMsgValue(value);
    }
  });
}

SmartHomeState _addAggDeviceReducer(
    SmartHomeState state, AddAggDeviceAction action) {
  addAggDevice(
      state: state,
      aggregationId: action.aggregationId,
      allDevices: action.allDevices,
      sortedAggList: action.sortedAggList);
  final (
    aggDeviceIdSetMap: Map<String, Set<String>> map,
    offlineAggEnabled: bool enable
  ) = _getAggDeviceMapAndOfflineAggEnabled(
      state.deviceState.allCardViewModelMap);
  state.aggregationState.aggDeviceIdSetMap = map;
  state.aggregationState.offlineAggEnabled = enable;
  return state;
}

SmartHomeState _removeAggDeviceReducer(
    SmartHomeState state, RemoveAggDeviceAction action) {
  removeAggDevice(
      state: state,
      aggregationId: action.aggregationId,
      allDevices: action.allDevices,
      deleteDevice: action.deleteDevice,
      sortedAggList: action.sortedAggList);
  final (
    aggDeviceIdSetMap: Map<String, Set<String>> map,
    offlineAggEnabled: bool enable
  ) = _getAggDeviceMapAndOfflineAggEnabled(
      state.deviceState.allCardViewModelMap);
  state.aggregationState.aggDeviceIdSetMap = map;
  state.aggregationState.offlineAggEnabled = enable;
  return state;
}

SmartHomeState _updateAggregationRoomOperatingReducer(
    SmartHomeState state, UpdateAggregationRoomOperateAction action) {
  updateAggregationRoomOperateAction(state, action.operatingRoomId,
      action.roomOperating, action.aggregationId);
  return state;
}

SmartHomeState _updateAggregationRoomIndexReducer(
    SmartHomeState state, UpdateAggregationRoomIndexAction action) {
  updateAggregationRoomIndexAction(
      state, action.currentIndex, action.aggregationId);
  return state;
}

SmartHomeState _updateAggregationCardReducer(
    SmartHomeState state, UpdateAggregationCardAction action) {
  updateAggregationCardAction(
      state, action.aggregationOperating, action.aggregationId);
  return state;
}

SmartHomeState _updateDeviceFilter(
    SmartHomeState state, UpdateDeviceFilterAction action) {
  // List<String> floorNameList = getFloorOrderList(action.filterMap.keys.toSet());
  final LinkedHashMap<String, DeviceFilterModel> deviceFilterMap =
      LinkedHashMap<String, DeviceFilterModel>();

  final DeviceFilterModel houseModel = DeviceFilterModel(
      SmartHomeConstant.deviceFilterSelectAll,
      SmartHomeConstant.deviceFilterSelectAll,
      state.familyState.familyId,
      action.allCategory);
  deviceFilterMap[SmartHomeConstant.deviceFilterHouse] = houseModel;

  final List<FloorModel> floorListByFamily =
      Family.getCurrentFamilySync()?.floorInfos ?? <FloorModel>[];

  // floorNameList.forEach((String floorName) {
  //   final Map<String, LinkedHashSet<String>> roomMap =
  //       action.filterMap[floorName] ?? <String, LinkedHashSet<String>>{};

  //   final List<String> roomNameList = getRoomOrderFromFamily(
  //       roomMap.keys.toSet(), floorListByFamily, floorName);
  //   roomNameList.forEach((String roomName) {
  //     final String roomId =
  //         state.deviceState.roomIdByFloorRoomNameMap[floorName + roomName] ??
  //             '';
  //     final DeviceFilterModel model = DeviceFilterModel(floorName, roomName,
  //         roomId, roomMap[roomName] ?? LinkedHashSet<String>());
  //     deviceFilterMap[
  //         floorNameList.length == 1 ? roomName : '$floorName$roomName'] = model;
  //   });
  // });

  // floorNameList = getFloorOrderList(
  //     floorListByFamily.map((FloorModel e) => e.floorName).toSet());

  // sort floors
  floorListByFamily.sort((FloorModel a, FloorModel b) {
    final int orderA = floorNameMapWithOrderId[getFloorName(a.floorName)] ?? 0;
    final int orderB = floorNameMapWithOrderId[getFloorName(b.floorName)] ?? 0;
    return orderA.compareTo(orderB);
  });

  floorListByFamily.forEach((FloorModel floorModel) {
    final String floorName = getFloorName(floorModel.floorName);

    // sort rooms
    floorModel.rooms
        .sort((RoomModel a, RoomModel b) => a.sortCode.compareTo(b.sortCode));
    floorModel.rooms.forEach((RoomModel roomModel) {
      final String roomName = roomModel.roomName;
      final String roomId = roomModel.roomId;
      final LinkedHashSet<String> categorySet =
          action.filterMap[floorName]?[roomName] ?? LinkedHashSet<String>();
      deviceFilterMap[floorListByFamily.length == 1
              ? roomName
              : '$floorName$roomName'] =
          DeviceFilterModel(floorName, roomName, roomId, categorySet);
    });
  });

  if (action.shareCategorySet.isNotEmpty) {
    deviceFilterMap[SmartHomeConstant.shareDeviceFlag] = DeviceFilterModel(
        '',
        SmartHomeConstant.shareDeviceFlag,
        SmartHomeConstant.shareDeviceFlag,
        action.shareCategorySet);
  }

  state.deviceState.cardShowFloor = floorListByFamily.length != 1;

  final List<DeviceFilterModel> filterModelList =
      deviceFilterMap.values.toList();
  final int tabIndex = filterModelList
      .map((DeviceFilterModel e) => e.roomId)
      .toList()
      .indexOf(state.deviceState.selectedRoomId);

  if (tabIndex >= 0) {
    SmartHomePresenter.changeTab(tabIndex);
    state.deviceState.deviceTabIndex = tabIndex;
    state.deviceState.selectedFloor = filterModelList[tabIndex].floorName;
    state.deviceState.selectedRoom = filterModelList[tabIndex].roomName;
    state.deviceState.selectedDeviceCategory =
        SmartHomeConstant.deviceFilterSelectAll;
  } else {
    updateDeviceFilterToInit(state);
  }
  state.deviceState.deviceFilterMap = deviceFilterMap;
  return state;
}

SmartHomeState _updateVoiceBoxDevice(
    SmartHomeState state, UpdateVoiceBoxDeviceAction c) {
  return websocketVoiceBoxReducer(state, c);
}

SmartHomeState _updateCameraAction(SmartHomeState state, CameraAction c) {
  return cameraReducer(state, c);
}

SmartHomeState _updateExcludeDeviceList(
    SmartHomeState state, UpdateExcludeDeviceList action) {
  state.deviceState.excludeDeviceList = action.excludeDeviceList;
  return state;
}

SmartHomeState _updatePopupComponentViewModel(
    SmartHomeState state, UpdatePopupComponentViewModelAction action) {
  state.deviceState.popupComponentViewModel = action.popupComponentViewModel;
  return state;
}

SmartHomeState _smallCardDragFinished(
    SmartHomeState state, SmallCardDragFinishedAction action) {
  state.deviceState.allSmallCardSortIdList = <String>[...action.vmList];
  state.deviceState.tabFilterMap = <TabFilterModel, List<String>>{
    ...state.deviceState.tabFilterMap,
    TabFilterModel(SmartHomeConstant.deviceFilterSelectAll,
        SmartHomeConstant.deviceFilterSelectAll): <String>[...action.vmList]
  };
  _updateScrollableScrollPhysicsState(state);
  return state;
}

// 更新tab是否可滑动属性
void _updateScrollableScrollPhysicsState(SmartHomeState state) {
  if (state.isScrollableScrollPhysics) {
    state.isScrollableScrollPhysics = !state.isEditState;
  }
}

SmartHomeState _updateScrollPhysicsState(
    SmartHomeState state, UpdateScrollableScrollPhysicsAction action) {
  _updateScrollableScrollPhysicsState(state);
  return state;
}

SmartHomeState _updateSmallCardListFilteredState(
    SmartHomeState state, UpdateSmallCardListFilteredStateAction action) {
  state.deviceState.isDeviceSmallCardUnfiltered = action.isUnfiltered;
  return state;
}

SmartHomeState _updateDeviceTabIndex(
    SmartHomeState state, UpdateDeviceTabIndexAction action) {
  state.deviceState.deviceTabIndex = action.index;
  if (action.index < state.deviceState.deviceFilterMap.length) {
    final String key =
        state.deviceState.deviceFilterMap.keys.toList()[action.index];
    final DeviceFilterModel? filterModel =
        state.deviceState.deviceFilterMap[key];
    if (filterModel != null) {
      state.deviceState.selectedFloor = filterModel.floorName;
      state.deviceState.selectedRoom = filterModel.roomName;
      state.deviceState.selectedRoomId = filterModel.roomId;
      final String category = state.deviceState.selectedDeviceCategory;
      if (!filterModel.categorySet.contains(category)) {
        state.deviceState.selectedDeviceCategory =
            SmartHomeConstant.deviceFilterSelectAll;
      }
    }
  }
  return state;
}

SmartHomeState _updateDeviceFilterToInitial(
    SmartHomeState state, UpdateDeviceFilterToInitialAction action) {
  state.deviceState.isFilterExpanded = true;
  return state;
}

SmartHomeState _updateDeviceFilterToNew(
    SmartHomeState state, DeviceFilterConfirmAction action) {
  if (action.selectedDeviceCategory !=
      SmartHomeConstant.deviceFilterSelectAll) {
    _updateAggregatedDeviceCategories(state);
  }
  state.deviceState.selectedFloor = action.selectedFloor;
  state.deviceState.selectedRoom = action.selectedRoom;
  state.deviceState.selectedRoomId = action.selectedRoomId;
  state.deviceState.selectedDeviceCategory = action.selectedDeviceCategory;

  int index = state.deviceState.deviceFilterMap.keys
      .toList()
      .indexOf(action.selectSpace);
  if (index == -1) {
    index = 0;
  }
  state.deviceState.deviceTabIndex = index;
  SmartHomePresenter.changeTab(index);

  return state;
}

void _updateAggregatedDeviceCategories(SmartHomeState state) {
  state.deviceState.allCardViewModelMap
      .forEach((String deviceId, CardBaseViewModel baseViewModel) {
    if (baseViewModel is DeviceCardViewModel) {
      baseViewModel.categorySet = <String>{};
    }
  });
  state.aggregationState.aggDeviceIdSetMap
      .forEach((String key, Set<String> deviceIdSet) {
    _handleAggDeviceCategory(state, deviceIdSet);
  });
}

void _handleAggDeviceCategory(SmartHomeState state, Set<String> deviceIdSet) {
  if (deviceIdSet.isEmpty) {
    return;
  }
  final Set<String> categorySet = <String>{};
  String aggregationParentId = '';
  for (final String deviceId in deviceIdSet) {
    final SmartHomeDevice? smartHomeDevice =
        state.deviceState.smartHomeDeviceMap[deviceId];
    if (smartHomeDevice != null) {
      final SmartHomeDeviceBasicInfo basicInfo = smartHomeDevice.basicInfo;
      final String categoryName = basicInfo.twoGroupingName.isNotEmpty
          ? basicInfo.twoGroupingName
          : basicInfo.categoryGrouping;
      if (basicInfo.aggregationParentId.isNotEmpty) {
        aggregationParentId = basicInfo.aggregationParentId;
      }
      categorySet.add(categoryName);
    }
  }

  _updateDeviceViewModel(state, aggregationParentId, categorySet);
}

void _updateDeviceViewModel(
    SmartHomeState state, String aggregationParentId, Set<String> categorySet) {
  if (aggregationParentId.isEmpty) {
    return;
  }
  final CardBaseViewModel? cardBaseViewModel =
      state.deviceState.allCardViewModelMap[aggregationParentId];
  if (cardBaseViewModel is DeviceCardViewModel) {
    cardBaseViewModel.categorySet = categorySet;
  }
}

SmartHomeState _updateDeviceMapAction(
    SmartHomeState state, DeviceMapUpdateAction action) {
  state.deviceState.deviceStatus = action.smallCardSortIdList.isEmpty
      ? DeviceStatus.noDevice
      : DeviceStatus.hasDevice;

  state.deviceState.smartHomeDeviceMap = action.smartHomeDeviceMap;
  state.deviceState.allCardViewModelMap = action.allCardViewModelMap;

  state.deviceState.allCardViewModelMap[report_for_repair_card_id] =
      ReportForRepairCardViewModel();

  state.deviceState.allCardViewModelMap[add_device_card_id] =
      AddDeviceCardViewModel();

  _updateUnsortedIdList(state);

  state.deviceState.tabFilterMap = action.tabFilterMap;

  if (state.giftPackState.newUserPackModelList.isNotEmpty) {
    addNewUserCardViewModelToAllCardViewModelMap(
        state.giftPackState.newUserPackModelList);
  }

  ///下面三个是给外层列表用的
  state.deviceState.smallCardSortIdList = action.smallCardSortIdList;
  state.deviceState.allSmallCardSortIdList = <String>[
    ...action.smallCardSortIdList
  ];

  final (
    aggDeviceIdSetMap: Map<String, Set<String>> map,
    offlineAggEnabled: bool enable
  ) = _getAggDeviceMapAndOfflineAggEnabled(action.allCardViewModelMap);
  state.aggregationState.aggDeviceIdSetMap = map;
  state.aggregationState.offlineAggEnabled = enable;
  _updateAggViewModelDevices(state);
  return state;
}

({Map<String, Set<String>> aggDeviceIdSetMap, bool offlineAggEnabled})
    _getAggDeviceMapAndOfflineAggEnabled(
        Map<String, CardBaseViewModel> allCardViewModelMap) {
  final Map<String, Set<String>> aggDeviceIdSetMap = <String, Set<String>>{};
  bool offlineAggEnabled = false;
  allCardViewModelMap
      .forEach((String deviceId, CardBaseViewModel baseViewModel) {
    if (baseViewModel is DeviceCardViewModel) {
      final SmartHomeDeviceBasicInfo basicInfo = baseViewModel.device.basicInfo;
      if (basicInfo.aggregationParentId.isNotEmpty) {
        final Set<String> deviceIdSet =
            aggDeviceIdSetMap[basicInfo.aggregationParentId] ?? <String>{};
        deviceIdSet.add(deviceId);
        aggDeviceIdSetMap[basicInfo.aggregationParentId] = deviceIdSet;
      } else if (isOfflineAgg(basicInfo.deviceId)) {
        offlineAggEnabled = true;
      }
    }
  });
  return (
    aggDeviceIdSetMap: aggDeviceIdSetMap,
    offlineAggEnabled: offlineAggEnabled
  );
}

void _updateAggViewModelDevices(SmartHomeState state) {
  state.aggregationState.aggDeviceIdSetMap
      .forEach((String aggDeviceId, Set<String> deviceIdSet) {
    final CardBaseViewModel? cardBaseViewModel =
        state.deviceState.allCardViewModelMap[aggDeviceId];
    if (cardBaseViewModel is AggNonNetViewModel) {
      cardBaseViewModel.nonNetDeviceIds = deviceIdSet;
    } else if (cardBaseViewModel is AggOfflineViewModel) {
      cardBaseViewModel.longOfflineDeviceIds = deviceIdSet;
    }
  });
}

SmartHomeState _updateCustomAttribute(
    SmartHomeState state, UpdateCustomAttributeAction action) {
  final SmartHomeDevice? device =
      state.deviceState.smartHomeDeviceMap[action.deviceId];
  device?.attributeMap[action.attribute.name] = action.attribute;
  return state;
}

SmartHomeState _updateDeviceViewModelByFoodNum(
    SmartHomeState state, FridgeFoodNumsChangedAction action) {
  action.foodNumList.forEach((FoodNumItem foodNumItem) {
    final SmartHomeDevice? device =
        state.deviceState.smartHomeDeviceMap[foodNumItem.deviceId];

    device?.attributeMap[FridgeFoodNumPresenter.fridgeFoodNumKey] =
        SmartHomeDeviceAttribute(
            name: FridgeFoodNumPresenter.fridgeFoodNumKey,
            value: '${foodNumItem.clipFoodNotReadCount}');
    device?.attributeMap[FridgeFoodNumPresenter.fridgeFoodManageUrlKey] =
        SmartHomeDeviceAttribute(
            name: FridgeFoodNumPresenter.fridgeFoodManageUrlKey,
            value: foodNumItem.linkUrl);
  });

  return state;
}

/// 更新厨下净水器的耗材名称和等级
SmartHomeState _updatePurifiedConsumableNameAndLevel(SmartHomeState state,
    UpdatePurifiedConsumableNameAndLevelAttributeAction action) {
  final SmartHomeDevice? device =
      state.deviceState.smartHomeDeviceMap[action.deviceId];
  action.attributes.forEach((SmartHomeDeviceAttribute attribute) {
    device?.attributeMap[attribute.name] = attribute;
  });
  return state;
}

SmartHomeState _updatePurifiedConsumeMap(
    SmartHomeState state, PurifiedConsumeLoadFinish action) {
  return state;
}

SmartHomeState _washDeviceDateLoadFinish(
    SmartHomeState state, WashDeviceDataLoadFinish action) {
  state.deviceState.allCardViewModelMap.forEach((_, CardBaseViewModel value) {
    if (value is WashDeviceViewModel &&
        action.basicInfo.contains(value.device.basicInfo.typeId)) {
      value.updateWashDeviceModelV3();
      _updateMultiRollerData(state, value);
    }
    // 更新洗衣机基础信息准备OK的typeId集合
    if (value is WashDeviceViewModel &&
        action.basicInfo.contains(value.device.basicInfo.typeId)) {
      state.deviceState.washInfoPreparedTypeIdSet
          .add(value.device.basicInfo.typeId);
    }
    // 更新洗衣机用户偏好信息准备OK的deviceId集合
    if (value is WashDeviceViewModel &&
        action.userPrefer.contains(value.deviceId)) {
      state.deviceState.washPreferPreparedDeviceIdSet.add(value.deviceId);
    }
  });
  return state;
}

SmartHomeState _updateDeviceAttribute(
    SmartHomeState state, UpdateDeviceAttributeAction action) {
  bool aggCameraUpdateDevsChanged = false;
  action.attributeMap.forEach((String key, DeviceAttributeModel value) {
    final SmartHomeDevice? device = state.deviceState.smartHomeDeviceMap[key];
    if (device is SmartHomeDevice) {
      device.updateAttribute(value);

      /// 更新 allCardViewModelMap
      final CardBaseViewModel? viewModel =
          state.deviceState.allCardViewModelMap[key];
      if (viewModel is CameraDeviceCardViewModel) {
        viewModel.updateDevice(device);
      } else if (viewModel is DeviceCardViewModel) {
        if (viewModel is WashDeviceViewModel) {
          viewModel.updateWashDeviceModelV3();

          /// 更新多筒洗衣机数据
          _updateMultiRollerData(state, viewModel);
        }

        /// 更新弹出框数据
        if (viewModel.popupFuncSet.containsKey(
            state.deviceState.popupComponentViewModel?.identification)) {
          viewModel.largeCardFuncMap;
          state.deviceState.popupComponentViewModel = viewModel.popupFuncSet[
              state.deviceState.popupComponentViewModel?.identification];
        }
      } else if (viewModel is VoiceBoxCardViewModel) {
        final VoiceBoxCardViewModel tmp = viewModel.updateDevice(device);
        state.deviceState.allCardViewModelMap[key] = tmp;
      }
      if (viewModel != null) {
        updateDeviceViewModelForAggregation(device,
            viewModel as DeviceCardViewModel, state.deviceState.cardShowFloor);
      }
      // 更新长期离线设备数据
      updateAggOfflineDevices(device, state);

      aggCameraUpdateDevsChanged |= updateAggCameraDevice(device, state);
    }
  });
  if (aggCameraUpdateDevsChanged) {
    commitUpdateAggCameraDevices(state);
  }

  return state;
}

SmartHomeState _updatePopupContext(
    SmartHomeState state, UpdatePopupContextAction action) {
  state.deviceState.popUpContext = action.context;
  if (action.context == null) {
    state.deviceState.isFilterExpanded = false;
  }
  return state;
}

SmartHomeState _updateSelectedFuncSetKey(
    SmartHomeState state, UpdateSelectedFuncSetKey action) {
  final CardBaseViewModel? viewModel =
      state.deviceState.allCardViewModelMap[action.deviceId];
  if (viewModel is DeviceCardViewModel) {
    viewModel.selectedFunctionSetKey = action.selectedFuncSetKey;
    viewModel.manualSelectedFuncSet = true;
  }

  return state;
}

SmartHomeState _clearDeviceListAction(
    SmartHomeState state, ClearDeviceListAction action) {
  state.deviceState.deviceStatus = DeviceStatus.unknow;
  state.deviceState.smartHomeDeviceMap.clear();
  state.deviceState.smallCardSortIdList.clear();
  state.deviceState.allCardViewModelMap.clear();
  state.deviceState.allSmallCardSortIdList.clear();
  DevLogger.info(
      tag: 'clearDeviceList',
      msg:
          'smartHomeDeviceMap-----${state.deviceState.smartHomeDeviceMap}-----smallCardSortIdList-----${state.deviceState.smallCardSortIdList}-----allCardViewModelMap-----${state.deviceState.allCardViewModelMap}-----');
  return state;
}

SmartHomeState _updateWashProgram(
    SmartHomeState state, UpdateWashProgram action) {
  return state;
}

SmartHomeState _clearDeviceDataAction(
    SmartHomeState state, ClearDeviceDataAction action) {
  state.deviceState.deviceTabIndex = null;
  return state;
}

SmartHomeState _updateDeviceStatusAction(
    SmartHomeState state, UpdateDeviceStatusAction action) {
  state.deviceState.deviceStatus = action.deviceStatus;
  return state;
}

// 更新设备报装报修、添加设备、新手礼包卡片List
void _updateUnsortedIdList(SmartHomeState state) {
  final List<String> _unsortedIdList = <String>[];
  if (state.deviceState.filterText != SmartHomeConstant.filter) {
    _unsortedIdList.add(add_device_card_id);
  } else {
    _unsortedIdList
        .addAll(<String>[report_for_repair_card_id, add_device_card_id]);
    // 新手礼包卡片
    if (state.giftPackState.newUserPackModelList.isNotEmpty) {
      _unsortedIdList.add(new_user_guide_card_id);
      if (state.giftPackState.newUserPackModelList.length > 1) {
        _unsortedIdList.add(new_user_gift_pack_card_id);
      }
    }
  }
  state.deviceState.unsortedIdList = _unsortedIdList;
}

SmartHomeState _imageRefreshCountReducer(
    SmartHomeState state, UpdateImageRefreshCountAction action) {
  state.imageRefreshCount += 1;
  return state;
}

void _updateMultiRollerData(
    SmartHomeState state, WashDeviceViewModel viewModel) {
  /// viewModel为附件设备属性上报，找到主设备，更新弹出框VM
  /// viewModel为主设备属性上报，直接更新弹出框VM
  final String parentId = viewModel.device.basicInfo.parentId;
  final String deviceRole = viewModel.device.basicInfo.deviceRole;
  if (parentId.isNotEmpty && deviceRole == '3') {
    final CardBaseViewModel? viewModel =
        state.deviceState.allCardViewModelMap[parentId];
    if (viewModel is WashDeviceViewModel) {
      viewModel.updateMultiRollerData();

      /// 更新卡片点击弹出框数据
      if ((viewModel.cardClickPopupComponent?.identification ?? '') ==
          state.deviceState.popupComponentViewModel?.identification) {
        state.deviceState.popupComponentViewModel =
            viewModel.cardClickPopupComponent;
      }
    }
  } else if (deviceRole != '3') {
    viewModel.updateMultiRollerData();
  }
}

void updateDeviceFilterToInit(SmartHomeState state) {
  state.deviceState.selectedFloor = SmartHomeConstant.deviceFilterSelectAll;
  state.deviceState.selectedRoom = SmartHomeConstant.deviceFilterSelectAll;
  state.deviceState.selectedRoomId = state.familyState.familyId;
  state.deviceState.deviceTabIndex = 0;
  SmartHomePresenter.changeTab(0);
  state.deviceState.selectedDeviceCategory =
      SmartHomeConstant.deviceFilterSelectAll;
}
