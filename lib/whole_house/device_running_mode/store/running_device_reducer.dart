import 'dart:collection';

import 'package:redux/redux.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_action.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_constant.dart';
import 'package:smart_home/whole_house/device_running_mode/store/running_device_state.dart';

final Reducer<SmartHomeState> deviceRunningReducer =
    combineReducers<SmartHomeState>(
  <Reducer<SmartHomeState>>[
    TypedReducer<SmartHomeState, UpdateDeviceAttributeAction>(
            _updateRunningDeviceStateReducer)
        .call,
    TypedReducer<SmartHomeState, WashDeviceDataLoadFinish>(
            _updateWashDeviceStateReducer)
        .call,
    TypedReducer<SmartHomeState, ClearRunningDeviceAction>(
            _clearRunningDeviceStateReducer)
        .call,
    TypedReducer<SmartHomeState, UpdateRunningDeviceInfoAction>(
            _updateRunningDeviceInfoReducer)
        .call,
  ],
);

SmartHomeState _updateWashDeviceStateReducer(
    SmartHomeState state, WashDeviceDataLoadFinish action) {
  _initAndUpdateRunningDevice(state, action);
  return state;
}

void _initAndUpdateRunningDevice(
    SmartHomeState state, DeviceBaseAction action) {
  final LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap =
      LinkedHashMap<String, List<RunningDeviceItemModel>>.from(
          state.wholeHouseState.runningDeviceState.runningDeviceMap);
  final Set<String> runningDeviceId = Set<String>.from(
      state.wholeHouseState.runningDeviceState.runningDeviceId);

  final Set<String> filteredRunningDeviceId =
      _removeUnbindDevice(state, runningDeviceId);

  List<CardBaseViewModel> sortedList = <CardBaseViewModel>[];
  if (runningDeviceMap.isEmpty && filteredRunningDeviceId.isEmpty) {
    sortedList = _handleRunningDeviceSortList(state);
    _handleSortedList(sortedList, runningDeviceMap, filteredRunningDeviceId);
  } else {
    // 运行中设备列表不为空，根据设备状态【未运行】->【运行中】进行排序
    /// - 如果该设备品类已有运行中设备，则将该设备品类图标放置在最前方，并且半屏弹窗中运行中设备列表第一个设备为该设备；
    /// - 如果该设备品类没有运行中的设备，则只将该设备品类图标放置在最前方；
    /// - 如果同时间上报多台运行中设备，按照设备列表排序；
    /// - 在当前家庭停留时，设备状态从【已运行】变成【未运行】，直接从排好序的运行中设备集合中删除对应的设备即可。
    final List<String> deviceIds = action is UpdateDeviceAttributeAction
        ? action.attributeMap.keys.toList()
        : state.deviceState.allCardViewModelMap.keys.toList();

    // 更新设备状态
    _updateRunningDevices(
        state, runningDeviceMap, filteredRunningDeviceId, deviceIds);
  }

  state.wholeHouseState.runningDeviceState.runningDeviceMap = runningDeviceMap;
  state.wholeHouseState.runningDeviceState.runningDeviceId =
      filteredRunningDeviceId.toList();
}

SmartHomeState _clearRunningDeviceStateReducer(
    SmartHomeState state, ClearRunningDeviceAction action) {
  state.wholeHouseState.runningDeviceState.clear();
  return state;
}

SmartHomeState _updateRunningDeviceInfoReducer(
    SmartHomeState state, UpdateRunningDeviceInfoAction action) {
  final LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap =
      LinkedHashMap<String, List<RunningDeviceItemModel>>.from(
          state.wholeHouseState.runningDeviceState.runningDeviceMap);
  final Set<String> runningDeviceId = Set<String>.from(
      state.wholeHouseState.runningDeviceState.runningDeviceId);

  if (runningDeviceMap.isEmpty || runningDeviceId.isEmpty) {
    return state;
  }

  _updateRunningDeviceInfo(state, runningDeviceMap, runningDeviceId);
  return state;
}

void _updateRunningDeviceInfo(
  SmartHomeState state,
  LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap,
  Set<String> runningDeviceId,
) {
  final Map<String, RunningDeviceItemModel> updatedDevices =
      <String, RunningDeviceItemModel>{};

  state.deviceState.allCardViewModelMap
      .forEach((String key, CardBaseViewModel value) {
    if (value is DeviceCardViewModel && value.runningMode) {
      updatedDevices[key] = RunningDeviceItemModel.fromDeviceCardVM(value);
    }
  });

  final LinkedHashMap<String, List<RunningDeviceItemModel>>
      filteredRunningDeviceMap =
      LinkedHashMap<String, List<RunningDeviceItemModel>>();

  runningDeviceMap.forEach((String key, List<RunningDeviceItemModel> element) {
    filteredRunningDeviceMap.putIfAbsent(key, () => <RunningDeviceItemModel>[]);
    for (int i = 0; i < element.length; i++) {
      final String deviceId = element[i].deviceId;
      if (updatedDevices.containsKey(deviceId)) {
        filteredRunningDeviceMap[key]!.add(updatedDevices[deviceId]!);
      }
    }
  });

  state.wholeHouseState.runningDeviceState.runningDeviceMap =
      filteredRunningDeviceMap;
  state.wholeHouseState.runningDeviceState.runningDeviceId =
      updatedDevices.keys.toList();
}

SmartHomeState _updateRunningDeviceStateReducer(
    SmartHomeState state, UpdateDeviceAttributeAction action) {
  /// 组装运行中设备模型
  _initAndUpdateRunningDevice(state, action);

  return state;
}

Set<String> _removeUnbindDevice(SmartHomeState state, Set<String> deviceId) {
  // 过滤掉解绑的设备
  final Set<String> allDeviceIds =
      state.deviceState.allCardViewModelMap.keys.toSet();
  final Set<String> intersection = deviceId.intersection(allDeviceIds);

  return intersection;
}

void _updateRunningDevices(
  SmartHomeState state,
  LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap,
  Set<String> runningDeviceIds,
  List<String> deviceIds,
) {
  final Set<String> tempRunningDeviceIds = <String>{};

  // 更新设备状态
  deviceIds.forEach((String key) {
    final SmartHomeDevice? device = state.deviceState.smartHomeDeviceMap[key];
    if (device is SmartHomeDevice) {
      _updateDeviceRunningState(
        state,
        device,
        runningDeviceMap,
        runningDeviceIds,
        tempRunningDeviceIds,
        key,
      );
    }
  });

  // 如果有多个设备需要排序
  if (tempRunningDeviceIds.length > 1) {
    _processSortedDevices(state, runningDeviceMap, tempRunningDeviceIds);
  }
}

void _processSortedDevices(
  SmartHomeState state,
  LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap,
  Set<String> tempRunningDeviceIds,
) {
  final List<CardBaseViewModel> sortedList =
      _sortAndMapDevices(state, tempRunningDeviceIds);

  // 删除之前添加的设备
  _removeDevicesFromMap(runningDeviceMap, sortedList, tempRunningDeviceIds);

  // 处理排序后的列表
  final LinkedHashMap<String, List<RunningDeviceItemModel>>
      tempRunningDeviceMap =
      LinkedHashMap<String, List<RunningDeviceItemModel>>.from(
          <String, List<RunningDeviceItemModel>>{});
  _handleSortedList(sortedList, tempRunningDeviceMap, tempRunningDeviceIds);

  // 合并临时映射表到原始映射表
  runningDeviceMap.forEach((String key, List<RunningDeviceItemModel> value) {
    if (tempRunningDeviceMap.containsKey(key)) {
      tempRunningDeviceMap[key]!.addAll(value);
    } else {
      tempRunningDeviceMap[key] = value;
    }
  });

  runningDeviceMap.clear();
  runningDeviceMap.addAll(tempRunningDeviceMap);
}

void _removeDevicesFromMap(
  LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap,
  List<CardBaseViewModel> sortedList,
  Set<String> tempRunningDeviceIds,
) {
  for (final CardBaseViewModel viewModel in sortedList) {
    if (viewModel is DeviceCardViewModel) {
      final String? categoryKey =
          _getDeviceCategory(viewModel.device.basicInfo.appTypeCode);
      if (categoryKey == null) {
        continue;
      }
      final String deviceId = viewModel.device.basicInfo.deviceId;
      if (tempRunningDeviceIds.remove(deviceId)) {
        runningDeviceMap[categoryKey]?.removeWhere(
            (RunningDeviceItemModel item) => item.deviceId == deviceId);
      }
    }
  }
}

void _updateDeviceRunningState(
  SmartHomeState state,
  SmartHomeDevice device,
  LinkedHashMap<String, List<RunningDeviceItemModel>> currentRunningDeviceMap,
  Set<String> runningDeviceIds,
  Set<String> tempRunningDeviceIds,
  String key,
) {
  final Map<String, CardBaseViewModel> viewModelMap =
      state.deviceState.allCardViewModelMap;
  final CardBaseViewModel? viewModel = viewModelMap[key];
  if (viewModel is DeviceCardViewModel &&
      !viewModel.isSharedDevice &&
      !viewModel.deviceRole3) {
    if (viewModel.runningMode) {
      _addDeviceToRunningMap(
        currentRunningDeviceMap,
        runningDeviceIds,
        tempRunningDeviceIds,
        viewModel,
      );
    } else {
      _removeDeviceFromRunningMap(
          currentRunningDeviceMap, runningDeviceIds, viewModel);
    }
  }
}

/// 获取设备的品类分类
String? _getDeviceCategory(String appTypeCode) {
  final Map<String, String>? categoryMap =
      RunningDeviceConstant.runningDeviceTypeCodeMap[appTypeCode];
  return categoryMap?[RunningDeviceConstant.categoryKey];
}

void _addDeviceToRunningMap(
  LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap,
  Set<String> runningDeviceIds,
  Set<String> tempRunningDeviceIds,
  DeviceCardViewModel viewModel,
) {
  final String? categoryKey =
      _getDeviceCategory(viewModel.device.basicInfo.appTypeCode);
  if (categoryKey == null) {
    return;
  }
  final String deviceId = viewModel.device.basicInfo.deviceId;

  if (runningDeviceIds.add(deviceId)) {
    tempRunningDeviceIds.add(deviceId);

    if (tempRunningDeviceIds.length == 1) {
      // 首次上报运行中设备
      if (runningDeviceMap.containsKey(categoryKey)) {
        // 将该设备品类图标放置在最前方
        final LinkedHashMap<String, List<RunningDeviceItemModel>>
            tempRunningDeviceMap =
            LinkedHashMap<String, List<RunningDeviceItemModel>>.from(
                <String, List<RunningDeviceItemModel>>{});
        final List<RunningDeviceItemModel> existingDevices =
            runningDeviceMap.remove(categoryKey)!;
        tempRunningDeviceMap[categoryKey] = <RunningDeviceItemModel>[
          RunningDeviceItemModel.fromDeviceCardVM(viewModel),
          ...existingDevices,
        ];
        tempRunningDeviceMap.addAll(runningDeviceMap);
        runningDeviceMap.clear();
        runningDeviceMap.addAll(tempRunningDeviceMap);
      } else {
        // 该设备品类没有运行中的设备
        final LinkedHashMap<String, List<RunningDeviceItemModel>>
            tempRunningDeviceMap =
            LinkedHashMap<String, List<RunningDeviceItemModel>>.from(
                runningDeviceMap);
        runningDeviceMap.clear();
        runningDeviceMap[categoryKey] = <RunningDeviceItemModel>[
          RunningDeviceItemModel.fromDeviceCardVM(
            viewModel,
          )
        ];
        runningDeviceMap.addAll(tempRunningDeviceMap);
      }
    }
  }
}

void _removeDeviceFromRunningMap(
  LinkedHashMap<String, List<RunningDeviceItemModel>> runningDeviceMap,
  Set<String> runningDeviceIds,
  DeviceCardViewModel viewModel,
) {
  final String? categoryKey =
      _getDeviceCategory(viewModel.device.basicInfo.appTypeCode);
  if (categoryKey == null) {
    return;
  }
  final String deviceId = viewModel.device.basicInfo.deviceId;

  if (runningDeviceIds.remove(deviceId)) {
    runningDeviceMap[categoryKey]?.removeWhere(
        (RunningDeviceItemModel item) => item.deviceId == deviceId);
  }
}

List<CardBaseViewModel> _sortAndMapDevices(
  SmartHomeState state,
  Set<String> deviceIds,
) {
  final List<CardBaseViewModel> sortedList =
      _sortAndGetViewModels(state, deviceIds.toList());
  return sortedList;
}

List<CardBaseViewModel> _sortAndGetViewModels(
  SmartHomeState state,
  List<String> deviceIds,
) {
  final Map<String, int> smallSortMap = _combineSmallSortMap(state);

  // 列表排序
  final List<String> tmpSmallCardSortIdList =
      _handleCardSortList(smallSortMap, deviceIds.toList());

  final List<CardBaseViewModel> sortedList =
      _generateSortedList(state, tmpSmallCardSortIdList);

  return sortedList;
}

Map<String, int> _combineSmallSortMap(SmartHomeState state) {
// 同时上报多个运行中设备，进行设备列表排序
  final Map<String, int> smallSortMap = <String, int>{};
  for (int i = 0; i < state.deviceState.allSmallCardSortIdList.length; i++) {
    final String deviceId = state.deviceState.allSmallCardSortIdList[i];
    smallSortMap[deviceId] = i;
  }

  return smallSortMap;
}

List<CardBaseViewModel> _generateSortedList(
    SmartHomeState state, List<String> tmpSmallCardSortIdList) {
  // 获取列表viewModel
  final List<CardBaseViewModel> sortedList = <CardBaseViewModel>[];
  final Map<String, CardBaseViewModel> viewModelMap =
      state.deviceState.allCardViewModelMap;
  for (int i = 0; i < tmpSmallCardSortIdList.length; i++) {
    final CardBaseViewModel? smartHomeDevice =
        viewModelMap[tmpSmallCardSortIdList[i]];
    if (smartHomeDevice is CardBaseViewModel) {
      sortedList.add(smartHomeDevice);
    }
  }

  return sortedList;
}

void _handleSortedList(
  List<CardBaseViewModel> sortedList,
  Map<String, List<RunningDeviceItemModel>> runningDeviceMap,
  Set<String> runningDeviceId,
) {
  for (int i = 0; i < sortedList.length; i++) {
    final CardBaseViewModel viewModel = sortedList[i];
    if (viewModel is DeviceCardViewModel &&
        !viewModel.isSharedDevice &&
        !viewModel.deviceRole3) {
      if (viewModel.runningMode) {
        _addDeviceToMap(runningDeviceMap, runningDeviceId, viewModel);
      } else {
        // 移除已上报运行中的设备
        _removeDeviceFromMap(runningDeviceMap, runningDeviceId, viewModel);
      }
    }
  }
}

/// 添加设备到映射表
void _addDeviceToMap(Map<String, List<RunningDeviceItemModel>> runningDeviceMap,
    Set<String> runningDeviceId, DeviceCardViewModel viewModel) {
  final String? key =
      _getDeviceCategory(viewModel.device.basicInfo.appTypeCode);
  if (key == null) {
    return;
  }
  runningDeviceMap.putIfAbsent(key, () => <RunningDeviceItemModel>[]);
  if (runningDeviceId.add(viewModel.device.basicInfo.deviceId)) {
    runningDeviceMap[key]!
        .add(RunningDeviceItemModel.fromDeviceCardVM(viewModel));
  }
}

/// 移除设备从映射表
void _removeDeviceFromMap(
    Map<String, List<RunningDeviceItemModel>> runningDeviceMap,
    Set<String> runningDeviceId,
    DeviceCardViewModel viewModel) {
  final String? key =
      _getDeviceCategory(viewModel.device.basicInfo.appTypeCode);
  if (key == null) {
    return;
  }
  if (runningDeviceId.remove(viewModel.device.basicInfo.deviceId)) {
    runningDeviceMap[key]?.removeWhere((RunningDeviceItemModel item) =>
        item.deviceId == viewModel.device.basicInfo.deviceId);
  }
}

bool _isValidDevice(DeviceCardViewModel device) {
  return device.device.basicInfo.deviceRole != '3';
}

List<CardBaseViewModel> _handleRunningDeviceSortList(SmartHomeState state) {
  final List<String> deviceIds = <String>[];

  final List<CardBaseViewModel> allCardViewModelMap =
      state.deviceState.allCardViewModelMap.values.toList();
  for (int i = 0; i < allCardViewModelMap.length; i++) {
    final CardBaseViewModel smartHomeDevice = allCardViewModelMap[i];
    if (smartHomeDevice is DeviceCardViewModel &&
        _isValidDevice(smartHomeDevice)) {
      deviceIds.add(smartHomeDevice.device.basicInfo.deviceId);
    }
  }

  final List<CardBaseViewModel> sortedList =
      _sortAndGetViewModels(state, deviceIds);
  return sortedList;
}

List<String> _handleCardSortList(
    Map<String, int> sortMap, List<String> keysList) {
  keysList.sort((String key1, String key2) {
    final int? sort1 = sortMap[key1];
    final int? sort2 = sortMap[key2];
    if (sort1 == null || sort2 == null) {
      return 0; // 默认排序
    }
    return sort1 - sort2;
  });
  return keysList;
}
