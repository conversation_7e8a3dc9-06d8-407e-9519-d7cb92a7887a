/*
 * 描述：xxxxxxxx
 * 作者：songFJ
 * 建立时间: 7/14/23
 * 
 */

import 'package:device_utils/typeId_parse/type_id_parse.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Constant {
  static const int onLineCode = 1;
  static const int powerOnCode = 2;
  static const int alarmCode = 4;
  static const String offlineHint = '相关设备已离线，请检查后再试';
  static const String wholeHouseCurtain = '全屋遮阳';
  static const String wholeHouseLight = '全屋照明';
  static const String networkUnavailable = '网络不可用';
  static const String allOpen = '全开';
  static const String allClose = '全关';
  static const String rightArrLocalImageUrl = 'assets/images/right_arr.webp';

  static const String regForSubtitle = '[0-9]';

  static const String tagSmartHomeWholeHouse = '[SmartHomeWholeHouse]';
  static const String tagProvider = '$tagSmartHomeWholeHouse[provider]';
  static const String tagPowerCommand = '$tagSmartHomeWholeHouse[PowerCommand]';
  static const String tagWholeHouseConsume = '$tagSmartHomeWholeHouse[Consume]';

  static const String packageName = 'smart_home';

  //照明房间卡片widget宽度 （16是左右外边距  12是左右内边距 7是卡片间间距）
  static double roomWgtWidth =
      (ScreenUtil().screenWidth - 16.w * 2 - 12.w * 2 - 7.w) / 2;
  static double singleRoomWgtWidth =
      ScreenUtil().screenWidth - 12.w * 2 - 16.w * 2;
//照明大卡 右上总控按钮宽度
  static double batchCtlBtnWidth = (roomWgtWidth - 2.w * 2 - 6.w) / 2;

  ///照明房间卡片显示房间名称的最大长度(且仅在设备状态有文本显示的时候控制长度)
  static int roomNameMaxLength = 4;
  static int roomSubTitleNameMaxLength = 7;

  static Color btnBorderColor = Colors.black.withAlpha(150);
  static const String tipForAlarmDevice = '设备故障，请检查后再试';
}

class ProviderConst {
  static const String keyAlarmCancelDefaultNativeWash =
      'alarmCancelDefaultNativewash';
  static const String devOtherStateDefault = '';
}

class LightStringConst {
  static const String gioLightDetailClick = 'MB34265'; //智家TAB-全屋-照明-点击系统卡片
  static const String gioLightRoomClick = 'MB34277'; //智家TAB-全屋-照明-点击房间卡片
  static const String gioLightTurnOnAllClick = 'MB34278'; //智家TAB-全屋-照明-点击系统全开
  static const String gioLightTurnOffAllClick = 'MB34279'; //智家TAB-全屋-照明-点击系统全关
  static const String gioLightTurnOnRoomClick = 'MB34280'; //智家TAB-全屋-照明-点击房间全开
  static const String gioLightTurnOffRoomClick = 'MB34281'; //智家TAB-全屋-照明-点击房间全关
  static const String uriLightOrCurtain = 'mpaas://secu_L2_wholeHouse';
  static String subfixForLightCountInRoom = '个房间亮灯';
  static String noRoomWithLight = '无房间亮灯';
  static String allLightOpen = '所有灯光已开启';
  static String allLightClose = '所有灯光已关闭';

  static String subfixForLightCount = '个灯亮';
  static String simpleAllLightClose = '全部灯关';

  static String localLightOpenImageUrl = 'assets/images/icon_light_open.webp';
  static String localLightCloseImageUrl = 'assets/images/icon_light_close.webp';

  static String topForLightOpen = '房间灯光已开启';
  static String topForLightClose = '房间灯光已关闭';

  static const String plateTag = '1';
  static const String plateName = '灯光';
}

class CurtainStringConst {
  static const String uriLightOrCurtain = 'mpaas://secu_L2_wholeHouse';
  static const String gioCurtainDetailClick = 'MB34259'; //智家TAB-全屋-遮阳-点击系统卡片
  static const String gioCurtainRoomClick = 'MB34260'; //智家TAB-全屋-遮阳-点击房间卡片
  static const String gioCurtainTurnOnAllClick = 'MB34261'; //智家TAB-全屋-遮阳-点击系统全开
  static const String gioCurtainTurnOffAllClick =
      'MB34262'; //智家TAB-全屋-遮阳-点击系统全关
  static const String gioCurtainTurnOnRoomClick =
      'MB34263'; //智家TAB-全屋-遮阳-点击房间全开
  static const String gioCurtainTurnOffRoomClick =
      'MB34264'; //智家TAB-全屋-遮阳-点击房间全关
  static const String keyCurtainStatus = 'curtainStatus';
  static const String valueCurtainOnOffStatus1 = '1';
  static const String valueCurtainOnOffStatus2 = '2';
  static const String valueCurtainOnOffStatus3 = '3';
  static String allCurtainOpen = '所有窗帘已开启';
  static String allCurtainClose = '所有窗帘已关闭';

  static const String curtainOpen = '窗帘开启';
  static const String curtainSubfixRoomOpen = '个房间窗帘开启';
  static const String curtainNoRoomOpen = '无房间窗帘开启';
  static const String curtainSubfixOpenNum = '个窗帘开启';
  static const String curtainOpenInRoom = '房间窗帘已开启';
  static const String curtainCloseInRoom = '房间窗帘已关闭';

  static const String curtainNoOpenNum = '无窗帘开启';

  static const String plateTag = '2';
  static const String plateName = '窗帘';
  static const String tag = 'CurtainViewModel';

  static const String curtainLocalCloseImageUrl =
      'assets/images/curtain_close.webp';

  static const String curtainLocalOpenImageUrl =
      'assets/images/curtain_open.webp';
  static const String curtainLocalOfflineImageUrl =
      'assets/images/curtain_offline.webp';
  static const String position = 'position';
  static const String openGegree = 'openDegree';
}

class CurtainLimit {
  ///不要乱改顺序
  /// 这个typeId会特殊取开合度
  static const String spec_curtain =
      '201c120024000810140200000000000000000000000000000000000000000040';

  static const String spec_curtain_2 =
      '201c80c70c50031c14132acaedd2bf00000083ff386b5bd068bb062a8b69d440';

  ///这个typeId会特殊取PowerState
  static const String spec_curtain_power_state =
      '201c80c70c50031c1413c001c47138000000ec24bf805b4531c836bfe2d0b740';
  static final List<String> curtainLimitList = <String>[spec_curtain, '14#13'];

  static bool isCurtainDevice(String typeId) {
    final String bigClass = TypeIdParse.firstTypeCode(typeId);
    final String middleClass = TypeIdParse.secondTypeCode(typeId);
    if (curtainLimitList.contains(typeId) ||
        curtainLimitList.contains(bigClass) ||
        curtainLimitList.contains('$bigClass#$middleClass')) {
      return true;
    }
    return false;
  }
}
