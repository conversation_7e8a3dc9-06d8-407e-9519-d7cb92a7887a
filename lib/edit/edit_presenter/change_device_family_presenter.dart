import 'package:device_utils/log/log.dart';
import 'package:family/device_brief_model.dart';
import 'package:family/device_manage_model.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:user/user.dart';

import '../../common/constant.dart';
import '../../common/constant_gio.dart';
import '../../common/full_screen_loading.dart';
import '../../device/device_view_model/device_card_view_model.dart';
import '../../navigator/family/family_viewmodel.dart';
import '../../service/http_service.dart';
import '../../store/smart_home_state.dart';
import '../../store/smart_home_store.dart';
import '../../widget_common/card_text_style.dart';
import '../edit_card_base_info_model.dart';
import '../model/change_family_check_model.dart';
import '../store/edit_action.dart';
import '../util/edit_constant.dart';
import '../util/edit_manager.dart';
import 'change_family_operate_check_presenter.dart';
import 'edit_presenter_manager.dart';

class ChangeDeviceFamilyPresenter {
  static List<FamilyItemModel> familyList = <FamilyItemModel>[];
  static String curFamilyId = '';
  static int currentIndex = 0;
  static FixedExtentScrollController? _scrollController =
      FixedExtentScrollController();

  static FamilyItemModel _selectFamily = FamilyItemModel(
      familyId: '',
      familyName: '',
      isOwner: false,
      createTime: 0,
      memberType: 2);

  static void showDialog(BuildContext context, bool isSelectNotRebindDevice) {
    _initFamilyInfo(isSelectNotRebindDevice);
    _selectFamily = familyList[currentIndex];
    EditPresenterManager.dialogs.showDoubleBtnModal<SmartHomeState>(
      context: context,
      title: EditConstant.changeDeviceFamily,
      enableDrag: true,
      child: (BuildContext dialogContext) {
        return _buildChangeFamilyDialogWidget();
      },
      confirmCallback: () {
        final List<String> deviceIds = <String>[];
        smartHomeStore.state.editState.selectedCardsList
            .forEach((EditDeviceCardInfo element) {
          deviceIds.add(element.id);
        });
        EditPresenterManager.onClickDone(
            saveHomeCardCallback: ({bool? isSuccess}) {
          _moveDevicesToFamily(deviceIds, _selectFamily.familyId);
        });
      },
    );
  }

  static void _initFamilyInfo(bool isSelectNotRebindDevice) {
    final List<FamilyItemModel> temFamilyList =
        smartHomeStore.state.familyState.familyModelList;
    final FamilyModel? familyModel = Family.getCurrentFamilySync();
    if (isSelectNotRebindDevice) {
      familyList = temFamilyList
          .where((FamilyItemModel element) => element.isOwner)
          .toList();
    } else {
      familyList = temFamilyList
          .where((FamilyItemModel element) =>
              element.isOwner || element.memberType == FamilyRole.admin.value)
          .toList();
    }
    curFamilyId = familyModel?.familyId ?? '';
    final int _index = familyList.indexWhere(
        (FamilyItemModel element) => element.familyId == curFamilyId);
    currentIndex = _index == -1 ? 0 : _index;
    _scrollController = FixedExtentScrollController(initialItem: currentIndex);
  }

  static void showFamilyChangeDialog(BuildContext context) {
    FullScreenLoading.show(context);
    final List<Map<String, String>> deviceList = <Map<String, String>>[];

    final List<String> canNotRebindList = <String>[];
    final Map<String, List<String>> map18010001 = <String, List<String>>{};
    final List<String> list18010004 = <String>[];
    final List<OperateCheckDialogMessageModel> dialogInfoList =
        <OperateCheckDialogMessageModel>[];
    final String oauthData = User.getOauthDataSync()?.uhome_user_id ?? '';
    smartHomeStore.state.editState.selectedCardsList
        .forEach((EditDeviceCardInfo element) {
      final Map<String, String> map = <String, String>{};
      map['deviceId'] = element.id;
      map['userId'] = element.ownUserId;
      map['psiUserId'] = element.ucUserId;
      deviceList.add(map);
      if (!element.isRebind && element.ownUserId != oauthData) {
        canNotRebindList.add(element.deviceName);
      }
    });

    if (canNotRebindList.isNotEmpty) {
      dialogInfoList.add(OperateCheckDialogMessageModel(
        desc: EditConstant.changeFamilySafeText,
        deviceNameList: canNotRebindList,
      ));
    }

    HttpService.operateCheck(deviceList).then((OperateCheckResponseModel? res) {
      FullScreenLoading.hide();
      DevLogger.debug(
          tag: SmartHomeEditManager.tag,
          msg: 'ChangeDeviceFamilyPresenter HttpService.operateCheck response: '
              'retCode: ${res?.retCode}, data: ${res?.data.deviceReasons}');
      if (res?.retCode != SmartHomeConstant.zjServerRetSuccessCode) {
        ToastHelper.showToast(EditConstant.reTry);
      } else {
        if (res is OperateCheckResponseModel &&
            (res.data.deviceReasons.reason1801001.isNotEmpty ||
                res.data.deviceReasons.reason1801004.isNotEmpty)) {
          if (res.data.deviceReasons.reason1801001.isNotEmpty) {
            res.data.deviceReasons.reason1801001
                .forEach((OperateCheckFailReasonModel element) {
              final DeviceCardViewModel? cardVm =
                  EditPresenterManager.getCardVm(element.deviceId);
              if (cardVm is DeviceCardViewModel) {
                if (map18010001[element.nickname] == null) {
                  map18010001[element.nickname] = <String>[];
                }
                map18010001[element.nickname]?.add(cardVm.deviceName);
              }
            });
          }
          if (res.data.deviceReasons.reason1801004.isNotEmpty) {
            res.data.deviceReasons.reason1801004
                .forEach((OperateCheckFailReasonModel element) {
              final DeviceCardViewModel? cardVm =
                  EditPresenterManager.getCardVm(element.deviceId);
              if (cardVm is DeviceCardViewModel) {
                list18010004.add(cardVm.deviceName);
              }
            });
          }

          map18010001.forEach((String key, List<String> value) {
            dialogInfoList.add(OperateCheckDialogMessageModel(
              userName: key,
              desc: EditConstant.changeFamilyPayText,
              deviceNameList: value,
            ));
          });

          if (list18010004.isNotEmpty) {
            dialogInfoList.add(OperateCheckDialogMessageModel(
              desc: EditConstant.changeFamilySelfDeviceText,
              deviceNameList: list18010004,
            ));
          }
        }

        if (dialogInfoList.isEmpty) {
          ChangeDeviceFamilyPresenter.showDialog(
              context, canNotRebindList.isNotEmpty);
        } else {
          gioTrack(GioConst.gioCheckDialog, <String, dynamic>{
            'origin': '移动家庭',
          });
          ChangeFamilyOperateCheckPresenter.showDialog(
            context,
            ChangeFamilyOperateCheckDialogModel(
              messageList: dialogInfoList,
            ),
          );
        }
      }
    }).catchError((dynamic e, StackTrace s) {
      ToastHelper.showToast(EditConstant.reTry);
      DevLogger.info(
          tag: SmartHomeEditManager.tag,
          msg: 'ChangeDeviceFamilyPresenter onClickChangeDeviceFamilyPosition '
              'http  error: $e, stackTrace: $s');
    }).whenComplete(() {
      FullScreenLoading.hide();
    });
  }

  static Widget _buildChangeFamilyDialogWidget() {
    return SizedBox(
      height: 220,
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox(
              height: 220,
              child: ScrollConfiguration(
                behavior: MyBehavior(),
                child: CupertinoPicker(
                  scrollController: _scrollController,
                  itemExtent: 44,
                  diameterRatio: 2.0,
                  useMagnifier: true,
                  squeeze: 1,
                  selectionOverlay: _selectionOverlayWidget(),
                  onSelectedItemChanged: (int index) {
                    _selectFamily = familyList[index];
                  },
                  children: familyList
                      .map((FamilyItemModel item) => Align(
                            child: Text(item.familyName,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 16,
                                    fontFamilyFallback: fontFamilyFallback(),
                                    fontWeight: FontWeight.w500,
                                    color: AppSemanticColors.item.primary)),
                          ))
                      .toList(),
                ),
              )),
        ],
      ),
    );
  }

  static Future<void> _moveDevicesToFamily(
      List<String> deviceIds, String newFamilyId) async {
    if (newFamilyId == curFamilyId) {
      return;
    }

    DevLogger.info(
        tag: SmartHomeEditManager.tag,
        msg: 'ChangeDeviceFamilyPresenter _moveDevicesToFamily '
            'request curFamilyId = $curFamilyId, newFamilyId = $newFamilyId,'
            ' deviceIds = $deviceIds');

    await Family.moveDevicesToFamily(curFamilyId, newFamilyId, deviceIds)
        .then((DeviceManageModel value) {
      DevLogger.info(
          tag: SmartHomeEditManager.tag,
          msg: 'ChangeDeviceFamilyPresenter _moveDevicesToFamily '
              'response = $value');

      if (value.successDevices.isNotEmpty && value.failureDevices.isEmpty) {
        //全部成功
        ToastHelper.showToast(EditConstant.changeFamilySuccess);
      } else if (value.successDevices.isNotEmpty &&
          value.failureDevices.isNotEmpty) {
        //部分成功
        final List<String> successDevice = <String>[];
        final List<String> failDevice = <String>[];
        value.successDevices.forEach((DeviceBriefModel element) {
          if (element.deviceName.isNotEmpty) {
            successDevice.add(element.deviceName);
          }
        });
        value.failureDevices.forEach((DeviceBriefModel element) {
          if (element.deviceName.isNotEmpty) {
            failDevice.add(element.deviceName);
          }
        });
        ToastHelper.showToast('${successDevice.join()}设备转移家庭成功，'
            '${failDevice.join()}设备转移家庭失败，请稍后再试');
      } else {
        DevLogger.info(
            tag: 'ChangeDeviceFamilyPresenter',
            msg: '_moveDevicesToFamily failed');
        ToastHelper.showToast(EditConstant.changeFamilyFail);
      }
    }).catchError((dynamic error) {
      DevLogger.error(
          tag: 'ChangeDeviceFamilyPresenter',
          msg: '_moveDevicesToFamily err: $error');
      ToastHelper.showToast(EditConstant.changeFamilyFail);
    });
  }
}

Widget _selectionOverlayWidget() {
  return Column(
    children: <Widget>[
      // TODO quzhenhao UI问题暂时使用分割线
      const Divider(height: 1, color: Colors.grey),
      Expanded(child: Container()),
      const Divider(height: 1, color: Colors.grey),
    ],
  );
}

class MyBehavior extends ScrollBehavior {
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const ClampingScrollPhysics();
  }
}
