import 'dart:async';

import 'package:connectivity/connectivity.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_interface.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/store/camera/camera_support.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:storage/storage.dart';

class CameraLivePresenter extends SmartHomeCameraPlayerInterfaceListener {
  void Function(CameraActionType)? _playCallback;

  CameraType cameraType = CameraType.HOME_CARD;

  String? _currentDeviceId;
  Store<SmartHomeState>? _store;

  String? typeId;
  String? devNo;
  String? brandCode;
  String? industryCode;
  String? model;

  bool get isSupportMultiStream => _cameraStreamCount == CameraStreamCount.two ;

  final CameraStreamCount _cameraStreamCount;

  final SmartHomeCameraPlayerInterface cameraPlayer;

  final String _tag = 'CameraLivePresenter';

  CameraLivePresenter(this.cameraPlayer, String deviceId, this.typeId,this._cameraStreamCount,
      {this.cameraType = CameraType.HOME_CARD}) {
    cameraPlayer.init();
    cameraPlayer.addListener(this);
    industryCode = CameraConstant.cmIndustryCode;
    _currentDeviceId = deviceId;
    stopCallback = (bool dispatchStop) {
      stopPlayer(_currentDeviceId!, dispatchStop: dispatchStop);
    };

    DevLogger.debug(
        tag: _tag,
        msg: 'CameraLivePresenter init deviceId = $_currentDeviceId $hashCode');

    Storage.setTemporaryStorage('isNeedHookFlutterDisplay', 'true');
  }

  void stopPlayer(String deviceId, {bool dispatchStop = true}) {
    DevLogger.debug(
        tag: _tag,
        msg:
            '$_tag stopPlayer device id is  $deviceId,dispathStop is $dispatchStop ');
    if (dispatchStop) {
      _store?.dispatch(CameraAction(
          CameraActionType.stop, CameraPayload(deviceId), cameraType));
    }
    stop(deviceId, (CameraActionType type) {});
  }

  Timer? _timer;
  Timer? _playTask;

  void cancelTimer() {
    _timer?.cancel();
  }

  void playWrapper(String deviceId) {
    Connectivity()
        .checkConnectivity()
        .then((ConnectivityResult connectivityResult) {
      if (connectivityResult == ConnectivityResult.none) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
      } else {
        _playWrapperInner(deviceId);
      }
    });
  }

  void _playWrapperInner(String deviceId) {
    _store?.dispatch(
      CameraAction(CameraActionType.play, CameraPayload(deviceId), cameraType),
    );
    play(deviceId, (CameraActionType type) {
      _store?.dispatch(CameraAction(type, CameraPayload(deviceId), cameraType));
    });
  }

  void play(String deviceId, void Function(CameraActionType) playCallback) {
    _playCallback = playCallback;
    _gioBtn(CameraConstant.gioPlay);
    if (_playTask != null) {
      _playTask!.cancel();
      _playTask = null;
    }
    final int playDelayTime = cameraPlayer.getPlayDelayTimes();
    DevLogger.info(
        tag: _tag,
        msg:
            'camera_live_widget livePlayerHelper play start after $playDelayTime seconds will invoke');
    _playTask = Timer(Duration(seconds: playDelayTime), () {
      DevLogger.info(
          tag: _tag, msg: 'camera_live_widget livePlayerHelper play');
      try {
        cameraPlayer.play();
      } catch (e) {
        DevLogger.info(
            tag: _tag,
            msg: 'camera_live_widget livePlayerHelper play error $e ');
      }
    });
  }

  void stopSleepDevice() {
    DevLogger.debug(
        tag: _tag,
        msg: '$_tag stopSleepDevice currentDev is ${_currentDeviceId ?? ''} ');
    if (_playTask != null) {
      _playTask?.cancel();
    }
    destroyPlayer();
    cameraPlayer.stop();
  }

  void wakeupWrapper(String deviceId) {
    _store?.dispatch(
      CameraAction(
          CameraActionType.wakeup, CameraPayload(deviceId), cameraType),
    );
    wakeup(deviceId);
    // widget.model.gioPlayAndStop(true);
    // widget.model.gioPlay(CameraGioModel(widget.model.device));
    CameraLiveCoordinator.instance.setPlayingDeviceIdAndPresenter(deviceId);
    play(deviceId, (CameraActionType type) {
      _store?.dispatch(CameraAction(type, CameraPayload(deviceId), cameraType));
    });
  }

  void wakeup(String deviceId) {
    DevLogger.debug(tag: _tag, msg: '$_tag wakeup $deviceId ');
    handleSleepButton(false, deviceId, _tag);
  }

  void retryWrapper(String deviceId) {
    _store?.dispatch(
      CameraAction(CameraActionType.retry, CameraPayload(deviceId), cameraType),
    );
    // widget.model.gioPlayAndStop(true);
    // _presenter?.gioPlay(CameraGioModel(widget.model.device));
    play(deviceId, (CameraActionType type) {
      _store?.dispatch(CameraAction(type, CameraPayload(deviceId), cameraType));
    });
  }

  void tapToolsBar(String deviceId) {
    cancelTimer();
    _store?.dispatch(
      CameraAction(CameraActionType.changeToolBarStatus,
          CameraPayload(deviceId), cameraType),
    );
  }

  void tapMute(String deviceId, bool isMute) {
    startTimer(const Duration(seconds: 3), () {
      _store?.dispatch(
        CameraAction(CameraActionType.changeToolBarStatus,
            CameraPayload(deviceId), cameraType),
      );
    });
    mute(deviceId, isMute);
    _store?.dispatch(
      CameraAction(CameraActionType.mute, CameraPayload(deviceId), cameraType),
    );
  }

  void startTimer(Duration duration, void Function() callback) {
    cancelTimer();
    _timer = Timer(duration, callback);
  }

  void mute(String deviceId, bool mute) {
    DevLogger.debug(tag: _tag, msg: '$_tag mute $deviceId ');
    _gioBtn(CameraConstant.gioMute);
    try {
      cameraPlayer.mute(mute);
    } catch (e) {
      DevLogger.debug(tag: _tag, msg: '$_tag mute error $e $deviceId ');
    }
  }

  void dispose() {
    DevLogger.debug(tag: _tag, msg: '$_tag dispose $_currentDeviceId');
    cameraPlayer.dispose();
  }

  void stopAndDistoryPlayer() {
    stopPlayWhenDisappear(_currentDeviceId!);
  }

  void stopPlayWhenDisappear(String deviceId) {
    if (playerMetux) {
      DevLogger.debug(
          tag: _tag,
          msg: '$_tag stopPlayWhenDisappear $deviceId return by metux');
      return;
    }
    DevLogger.debug(tag: _tag, msg: '$_tag stopPlayWhenDisappear $deviceId ');
    _store?.dispatch(
      CameraAction(CameraActionType.stop, CameraPayload(deviceId), cameraType),
    );
    stop(deviceId, (CameraActionType type) {});
  }

  void destroyPlayer() {
    if (playerMetux) {
      DevLogger.debug(
          tag: _tag,
          msg: '$_tag destroyPlayer $_currentDeviceId return by metux');
      return;
    }

    DevLogger.debug(tag: _tag, msg: '$_tag destroyPlayer $_currentDeviceId ');
    if (_playTask != null) {
      _playTask!.cancel();
      _playTask = null;
    }
    try {
      cameraPlayer.destroyPlayer();
    } catch (e) {
      DevLogger.debug(
          tag: _tag, msg: '$_tag destroyPlayer $_currentDeviceId error $e');
    }
  }

  void stop(String deviceId, void Function(CameraActionType) playCallback) {
    if (_playTask != null) {
      _playTask!.cancel();
      _playTask = null;
    }
    cancelTimer();
    _playCallback = playCallback;

    DevLogger.info(tag: _tag, msg: 'camera_live_widget livePlayerHelper stop');
    try {
      destroyPlayer();
      cameraPlayer.stop();
    } catch (e) {
      DevLogger.info(
          tag: _tag, msg: 'camera_live_widget livePlayerHelper stop erro $e');
    }
  }

  void tapPause(String deviceId) {
    _gioBtn(CameraConstant.gioStop);
    stopIndeed();
  }

  void tapPlayer(String deviceId) {
    startTimer(const Duration(seconds: 3), () {
      _store?.dispatch(
        CameraAction(CameraActionType.changeToolBarStatus,
            CameraPayload(deviceId), cameraType),
      );
    });
    _store?.dispatch(
      CameraAction(CameraActionType.changeToolBarStatus,
          CameraPayload(deviceId), cameraType),
    );
  }

  void stopIndeed() {
    if (playerMetux) {
      DevLogger.debug(
          tag: _tag, msg: '$_tag stopIndeed $_currentDeviceId return by metux');
      return;
    }
    _store?.dispatch(CameraAction(
        CameraActionType.stop, CameraPayload(_currentDeviceId!), cameraType));
    stop(_currentDeviceId!, (CameraActionType type) {});
  }

  void _gioBtn(String buttonName) {
    if (_currentDeviceId != null && _currentDeviceId!.isNotEmpty) {
      gioTrack(CameraConstant.gioPlayingID, <String, String>{
        CameraConstant.contentType: CameraConstant.contentTypeBigCard,
        CameraConstant.buttonName: buttonName,
      });
    }
  }

  void _gioPlaying() {
    if (_currentDeviceId != null && _currentDeviceId!.isNotEmpty) {
      gioTrack(CameraConstant.gioPlayingID, <String, String>{
        CameraConstant.deviceId: _currentDeviceId!,
        CameraConstant.typeid: typeId ?? '',
        CameraConstant.devno: devNo ?? '',
        CameraConstant.industryCode: industryCode ?? '',
        CameraConstant.model: model ?? '',
      });
    }
  }

  bool playerMetux = false;

  Widget currentPlayer(double w, double h,
      {Orientation orientation = Orientation.portrait}) {
    if (playerMetux) {
      orientation = Orientation.landscape;
    }
    final Widget playerWidget =
        cameraPlayer.createPlayer(w, h, orientation: orientation);
    DevLogger.debug(
        tag: _tag,
        msg:
            ' currentPlayer $playerWidget${playerWidget.hashCode} $_currentDeviceId $hashCode , h = $h w = $w');
    return playerWidget;
  }

  void lockPlayer() {
    playerMetux = true;
    DevLogger.debug(tag: _tag, msg: 'add player metux $_currentDeviceId');
  }

  void unlockPlayer() {
    playerMetux = false;
    DevLogger.debug(tag: _tag, msg: 'unlock player metux $_currentDeviceId');
  }

  void stopAndDestory() {
    destroyPlayer();
    stop(_currentDeviceId!, (CameraActionType p0) {});
  }

  void init(Store<SmartHomeState>? store) {
    _store = store;
    final SmartHomeDevice? dev =
        store?.state.deviceState.smartHomeDeviceMap[_currentDeviceId];
    devNo = dev?.basicInfo.prodNo ?? '';
    model = dev?.basicInfo.model ?? '';
    brandCode = dev?.basicInfo.brand ?? '';
  }

  void Function(bool dispatchStopStyle)? stopCallback;
  Function(CameraActionType) get playCallback =>
      _playCallback ?? (CameraActionType actionType) {};

  @override
  void onCameraPlayerError(String error) {
    if (_playCallback != null) {
      _playCallback!(CameraActionType.error);
    }
  }

  @override
  void onCameraPlayerPlaying() {
    _gioPlaying();
    if (_playCallback != null) {
      _playCallback!(CameraActionType.playing);
    }
  }

  @override
  void onCameraPlayerStopped() {
    if (_playCallback != null) {
      _playCallback!(CameraActionType.stop);
    }
  }

  void switchCameraStream(CameraWidgetStyle style, VoidCallback refreshCamera) {
    DevLogger.debug(
        tag: _tag,
        msg: 'switchCameraStream $_currentDeviceId current style $style');
    if (!isSupportMultiStream ||
        style == CameraWidgetStyle.offline) {
      return;
    }

    cameraPlayer.switchCameraStream();
    refreshCameraStream(
        _currentDeviceId ?? '', cameraPlayer.getCameraStream(), cameraType);

    if (style == CameraWidgetStyle.playing) {
      refreshCamera();
      return;
    }

    _handleCameraStyleAction(style);
  }

  void _handleCameraStyleAction(CameraWidgetStyle style) {
    final String deviceId = _currentDeviceId ?? '';
    switch (style) {
      case CameraWidgetStyle.sleep:
        wakeupWrapper(deviceId);
        break;
      case CameraWidgetStyle.stop:
        playWrapper(deviceId);
        break;
      case CameraWidgetStyle.retry:
        retryWrapper(deviceId);
        break;
      default:
        break;
    }
  }

  void tapFullscreen(BuildContext context, void Function() refreshCamera) {
    DevLogger.debug(tag: _tag, msg: 'tapFullscreen $_currentDeviceId ');
    smartHomeStore.dispatch(CameraAction(CameraActionType.enterFullScreen,
        CameraPayload(_currentDeviceId ?? ''), cameraType));
    cancelTimer();
    cameraPlayer.goToFullScreenCameraPage(context).then((bool needRefresh) {
      DevLogger.debug(
          tag: _tag,
          msg:
              'tapFullscreen back2card $_currentDeviceId ,needRefresh = $needRefresh');
      smartHomeStore.dispatch(CameraAction(
          CameraActionType.exitFullScreen,
          CameraPayload(_currentDeviceId ?? '', status: cameraPlayer.isMute()),
          cameraType));
      if (isSupportMultiStream) {
        if (needRefresh) {
          refreshCamera();
          refreshCameraStream(_currentDeviceId ?? '',
              cameraPlayer.getCameraStream(), cameraType);
        }
      }
    });
  }

  void setCameraStream(CameraStreamType cameraStreamType) {
    cameraPlayer.setCameraStream(cameraStreamType);
  }

  CameraStreamType getCameraStream() {
    return cameraPlayer.getCameraStream();
  }

  CameraStreamType? getCameraStreamIfNotSupport() {
    if (isSupportMultiStream) {
      return cameraPlayer.getCameraStream();
    }
    return null;
  }
}

void refreshCameraStream(
  String devId,
  CameraStreamType cameraStreamType,
  CameraType cameraType,
) {
  _refreshCameraStream(
      cameraType == CameraType.AGGREGATION_CARD
          ? CameraLiveCoordinator.instance
          : AggregationCameraPresenterManager.instance,
      devId,
      cameraStreamType);
}

void _refreshCameraStream(BasePresenterManager manager, String devId,
    CameraStreamType cameraStreamType) {
  if (manager.containPresenter(devId)) {
    manager.getPresenterByDevId(devId).setCameraStream(cameraStreamType);
  }
}
