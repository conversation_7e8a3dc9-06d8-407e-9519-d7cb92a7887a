import 'package:flutter/material.dart';
import 'package:reselect/reselect.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/whole_house/device_env/env_device_state.dart';
import 'package:smart_home/whole_house/location_weather/models/outdoor_weather_model.dart';
import 'package:smart_home/whole_house/location_weather/whole_house_header_weather_vm.dart';

import '../device_env/env_device_constants.dart';

/// 全屋头部天气选择器，用于从Redux状态中提取数据
class WeatherSelectors {
  /// 创建天气视图模型选择器
  static final Selector<SmartHomeState, WholeHouseHeaderWeatherViewModel>
      weatherViewModelSelector = createSelector3(
    (SmartHomeState state) =>
        state.wholeHouseState.environmentState.outdoorWeatherState,
    (SmartHomeState state) => state.wholeHouseState.areaState.areaName,
    (SmartHomeState state) => state.wholeHouseState.envDeviceState,
    _createViewModel,
  );

  static WholeHouseHeaderWeatherViewModel weatherViewModelWithRoomIdSelector(
      SmartHomeState state, String roomId) {
    final WholeHouseHeaderWeatherViewModel defaultViewModel =
        weatherViewModelSelector(state);

    return WholeHouseHeaderWeatherViewModel(
      icon: defaultViewModel.icon,
      value: defaultViewModel.value,
      unit: defaultViewModel.unit,
      backgroundColor: defaultViewModel.backgroundColor,
      customBackgroundImage: defaultViewModel.customBackgroundImage,
      visible: state.familyState.familyId == roomId &&
          defaultViewModel.visible &&
          !(state.wholeHouseState.envDeviceState.isNotEmpty &&
              state.wholeHouseState.envDeviceState.hasAnyOnlineDevice(roomId)),
      area: defaultViewModel.area,
    );
  }

  /// 根据输入参数计算天气视图模型
  static WholeHouseHeaderWeatherViewModel _createViewModel(
    OutdoorWeatherModel? outdoorWeather,
    String areaName,
    EnvDeviceState envDeviceState,
  ) {
    final String icon = _getWeatherIcon(outdoorWeather);
    final String value = _formatTemperature(outdoorWeather?.temperature);
    final String unit =
        EnvDeviceConstants.getUnit(EnvDeviceConstants.TYPE_TEMPERATURE);

    final String customBackgroundImage =
        outdoorWeather?.customBackgroundImage ?? '';
    final Color backgroundColor = _getBackgroundColor(outdoorWeather?.skycon);

    // 确定可见性
    final bool visible = outdoorWeather != null && areaName.isNotEmpty;

    return WholeHouseHeaderWeatherViewModel(
      icon: icon,
      value: value,
      unit: unit,
      backgroundColor: backgroundColor,
      customBackgroundImage: customBackgroundImage,
      visible: visible,
      area: areaName,
    );
  }

  static String _getWeatherIcon(OutdoorWeatherModel? weather) {
    if (weather == null) {
      return '';
    }
    return weather.customIcon;
  }

  static String _formatTemperature(String? temperature) {
    if (temperature == null) {
      return '';
    }
    return temperature;
  }

  /// 根据天气现象获取背景颜色
  static Color _getBackgroundColor(String? skycon) {
    const Color defaultColor = Color(0xFF3C7BF9);
    return defaultColor;
  }
}
