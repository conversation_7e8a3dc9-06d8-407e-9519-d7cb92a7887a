import 'package:connectivity/connectivity.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_card_model.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/agg_camera/model/agg_sort_response_model.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_action.dart';
import 'package:smart_home/device/aggregation/utils/agg_utils.dart';
import 'package:smart_home/edit/util/edit_constant.dart';
import 'package:smart_home/service/http_service.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';

class CameraAggPresenter {
  static Future<void> getCameraAggSortedList() async {
    final String familyId = Family.getCurrentFamilySync()?.familyId ?? '';
    try {
      final AggDetailSortModel? model =
          await HttpService.queryAggDetailSort(familyId, AggTypeEnum.cameraAgg);
      DevLogger.info(
          tag: tag,
          msg: '$tag getCameraAggSortedList '
              'response = [$model]');
      if (model != null) {
        bool isLarge = false;
        final Set<String> data = <String>{};
        model.aggSortList.forEach((AggSortModel element) {
          if (element.cardStatus == '2') {
            isLarge = true;
          }
          if (element.deviceId.isNotEmpty) {
            data.add(element.deviceId);
          }
        });
        smartHomeStore.dispatch(UpdateCameraAggListAction(isLarge, data));
      }
    } catch (e) {
      DevLogger.error(
          tag: tag,
          msg: '$tag getCameraAggSortedList failed '
              'e = $e');
    }

    return Future<void>.value();
  }

  static String tag = 'CameraAggPresenter';

  static Future<bool> saveAggCameraData2FamilyPlugin(
      Store<SmartHomeState> store,
      List<String> devs,
      String fmId,
      bool isLarge) async {
    final ConnectivityResult netResult =
        await Connectivity().checkConnectivity();
    if (netResult == ConnectivityResult.none) {
      ToastHelper.showToast(EditConstant.netError);
      return false;
    }
    final AggCard card =
        AggCard(aggType: '5', sortList: devs); //构建一个空的sortList塞进去.
    if (isLarge) {
      card.bigCardList = devs;
      card.smallCardList = <String>[];
    } else {
      card.smallCardList = devs;
      card.bigCardList = <String>[];
    }
    DevLogger.info(
        tag: tag,
        msg: '$tag saveAggregationIdList '
            'param is $fmId , aggCard $card');
    try {
      final DeviceCardAggregationModel model =
          DeviceCardAggregationModel(familyId: fmId, aggCard: <AggCard>[card]);

      final DeviceCardResult responseModel =
          await Family.operateDeviceCardAggregation(model);
      DevLogger.info(
          tag: tag,
          msg: '$tag saveAggregationIdList '
              'response = $responseModel');

      if (responseModel.retCode ==
          SmartHomeConstant.logicEngineOperateSuccessCode) {
        CameraAggPresenter.getCameraAggSortedList();
      } else {
        ToastHelper.showToast('保存失败,请稍后再试');
      }
      return true;
    } catch (e) {
      ToastHelper.showToast('保存失败,请稍后再试');
      DevLogger.error(
          tag: tag,
          msg: '$tag saveAggregationIdList failed '
              'e = $e');
      return false;
    }
  }
}
