import 'package:connectivity/connectivity.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/aggregation/aggregation_card/util/aggregation_device_constant.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/widgets/aggregation_camera_page.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/util/aggregation_setting_constant.dart';
import 'package:smart_home/device/component/widget/large_card_bottom_widget.dart';
import 'package:smart_home/device/component_view_model/component_view_model.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../../../edit/edit_presenter/edit_presenter_manager.dart';

class AggregationCameraCardViewModel extends DeviceCardViewModel {
  AggregationCameraCardViewModel({required super.device});

  int sleepCount = 0;
  int totalCount = 0;
  int offlineCount = 0;
  int onlineCount = 0;

  @override
  void cardClick(BuildContext? context, {String? jumpUrl}) {
    Connectivity().checkConnectivity().then((ConnectivityResult result) {
      gioTrack(AggregationDeviceConstant.cardClickGio,
          <String, dynamic>{'sourceName': '摄像头', 'value': '进详情页'});
      if (result == ConnectivityResult.none) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
        return;
      }
      DevLogger.info(tag: 'AggregationCameraCardViewModel', msg: '点击摄像头聚合卡片');
      smartHomeStore.dispatch(CameraAction(
          CameraActionType.stopAll, CameraPayload(''), CameraType.HOME_CARD));
      if (context != null) {
        EditPresenterManager.changePageWithHomeAndAgg(EditPageType.camera);
        Navigator.of(context).push(MaterialPageRoute<void>(
          builder: (BuildContext context) {
            return const AggregationCameraPage();
          },
        ));
      }
    });
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      super == other &&
          other is AggregationCameraCardViewModel &&
          runtimeType == other.runtimeType &&
          onlineCount == other.onlineCount &&
          sleepCount == other.sleepCount &&
          totalCount == other.totalCount &&
          offlineCount == other.offlineCount;

  @override
  int get hashCode =>
      super.hashCode ^ onlineCount ^ sleepCount ^ totalCount ^ offlineCount;

  @override
  String get deviceIcon => onlineCount > 0
      ? 'assets/icons/camera_on.webp'
      : 'assets/icons/camera_off.webp';

  @override
  String get deviceName => '摄像头';

  @override
  bool get isDeviceIconAsset => true;

  @override
  bool get deviceOffline => false;

  @override
  bool get supportLargeCard => true;

  @override
  String get unionSmallCardStatus => getDescription();

  @override
  String get unionMiddleCardStatus => getDescription();

  @override
  String get unionLargeCardStatus {
    return getCountDescription();
  }

  String getCountDescription() {
    return '$totalCount个摄像头';
  }

  @override
  int get deviceCount => totalCount;

  String getDescription() {
    if (totalCount <= 0) {
      return '暂无摄像头';
    }
    if (totalCount <= sleepCount ||
        totalCount <= offlineCount ||
        (sleepCount + offlineCount) == totalCount) {
      return '摄像头全关着';
    } else {
      return '$onlineCount个摄像头开启';
    }
  }

  @override
  Map<String, LargeDeviceCardFunctionSet> get unionLargeCardFuncMap {
    final String cameraAggKey = generateCameraAggreKey();
    selectedFunctionSetKey = cameraAggKey;

    return <String, LargeDeviceCardFunctionSet>{
      cameraAggKey: LargeDeviceCardFunctionSet(
          name: cameraAggKey,
          componentAlign: ComponentAlign.noInterval,
          componentViewModelList: getRoomListViewModel(
            getDescription(),
            (totalCount == 0)
                ? 'assets/icons/aggr_large_bttom_empty_camera.webp'
                : '',
          ))
    };
  }

  String generateCameraAggreKey() {
    return '摄像头卡片聚合$onlineCount=$totalCount=$offlineCount=$sleepCount';
  }

  void updateInfo(
      int onlineCount, int totalCount, int offlineCount, int sleepCount) {
    this.onlineCount = onlineCount;
    this.totalCount = totalCount;
    this.offlineCount = offlineCount;
    this.sleepCount = sleepCount;
    DevLogger.info(
        tag: 'AggregationCameraCardViewModel',
        msg:
            'updateInfo  onlineCount = $onlineCount, totalCount =$totalCount, offlineCount = $offlineCount, sleepCount =$sleepCount');
  }
}

List<ComponentBaseViewModel> getRoomListViewModel(
    String desc, String? iconPath) {
  return <ComponentBaseViewModel>[
    LargeCardBottomWidgetViewModel(desc, iconPath)
  ];
}

int setHashCode(Set<String> list) {
  int hashCode = 0;
  for (final dynamic element in list) {
    hashCode = hashCode ^ element.hashCode;
  }
  return hashCode;
}
