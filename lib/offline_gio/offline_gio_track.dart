import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:device_utils/log/log.dart';
import 'package:library_widgets/common/util.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/store/device_state.dart';
import 'package:smart_home/offline_gio/offline_gio_config.dart';
import 'package:smart_home/offline_gio/offline_gio_track_model.dart';
import 'package:smart_home/store/smart_home_store.dart';

class RepeatingTimer {
  RepeatingTimer(this.repeatingDuration);

  Timer? _timer;
  final Duration repeatingDuration;

  void startTimer(void Function() repeatingAction) {
    if (_timer?.isActive ?? false) {
      return;
    }

    _timer = Timer.periodic(repeatingDuration, (_) {
      repeatingAction();
    });
  }

  void cancelTimer() {
    _timer?.cancel();
  }
}

class OfflineGioTrack {
  factory OfflineGioTrack() {
    return _instance;
  }

  OfflineGioTrack._internal();

  static final OfflineGioTrack _instance = OfflineGioTrack._internal();

  //标识是否上传过
  final List<String> _deviceOfflineList = <String>[];

  final HashMap<String, OfflineCountModel> _typeCodeMap =
      HashMap<String, OfflineCountModel>();

  final List<String> _offlineDeviceIds = <String>[];

  String? currentFamilyId;

  //家庭被切换
  void updateFamilyId(String familyId) {
    try {
      if (currentFamilyId != familyId) {
        currentFamilyId = familyId;
        _deviceOfflineList.clear();
        _repeatingTimer.cancelTimer();
      }
    } catch (e) {
      DevLogger.info(tag: 'OfflineGioTrack', msg: 'updateFamilyId e:$e');
    }
  }

  final RepeatingTimer _repeatingTimer =
      RepeatingTimer(const Duration(seconds: 10));

  void collectDeviceData() {
    if (currentFamilyId == null) {
      return;
    }
    _repeatingTimer.cancelTimer();
    _repeatingTimer.startTimer(() {
      _calculateDeviceState();
      _repeatingTimer.cancelTimer();
    });
  }

  Future<void> _calculateDeviceState() async {
    final DeviceState deviceState = smartHomeStore.state.deviceState;
    final OfflineCountModel countModel = OfflineCountModel.defaultModel();
    _typeCodeMap.clear();
    _offlineDeviceIds.clear();

    //移除所有非网器设备
    final List<CardBaseViewModel> viewModelList = <CardBaseViewModel>[];
    deviceState.allCardViewModelMap.values
        .forEach((CardBaseViewModel viewModel) {
      if (viewModel is! DeviceCardViewModel) {
        return;
      }
      final SmartHomeDeviceBasicInfo basicInfo = viewModel.device.basicInfo;
      if (!_isNeedMarked(basicInfo)) {
        return;
      }

      viewModelList.add(viewModel);
    });

    int familyDeviceCount = 0;
    viewModelList.forEach((CardBaseViewModel viewModel) {
      if (viewModel is! DeviceCardViewModel) {
        return;
      }

      final String deviceId = viewModel.deviceId;
      if (_deviceOfflineList.contains(deviceId)) {
        return;
      }
      familyDeviceCount = familyDeviceCount + 1;
      _deviceOfflineList.add(deviceId);

      final SmartHomeDeviceBasicInfo basicInfo = viewModel.device.basicInfo;
      if (basicInfo.onlyConfigState !=
          UpDeviceOnlyConfigState.UN_CONFIGURABLE) {
        if (basicInfo.faultInformationStateCode == '1002' ||
            basicInfo.faultInformationStateCode == '1004') {
          gioTrack('MB37967', <String, dynamic>{
            'onlyConfigState': basicInfo.onlyConfigState.toString(),
            'faultInformationStateCode': basicInfo.faultInformationStateCode,
            'deviceid': deviceId,
            'devicetype': basicInfo.appTypeName,
          });
        }
      }
      if (!OfflineGIOConfig().isInRange(basicInfo.appTypeCode)) {
        return;
      }

      final String appTypeCode = basicInfo.appTypeCode;
      final OfflineStatusModel statusModel = OfflineStatusModel(viewModel);
      if (_typeCodeMap[appTypeCode] is OfflineCountModel) {
        final OfflineCountModel appCodeModel = _typeCodeMap[appTypeCode]!;
        _updateOfflineCountModel(
            statusModel, appCodeModel, countModel, _offlineDeviceIds);
      } else {
        //创建新的类
        final OfflineCountModel appCodeModel = OfflineCountModel.defaultModel();
        _updateOfflineCountModel(
            statusModel, appCodeModel, countModel, _offlineDeviceIds);
        _typeCodeMap[appTypeCode] = appCodeModel;
      }
    });

    final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
    final String appVersion = appInfo.appVersion;

    // 设备Tab-可见离线设备数
    if (familyDeviceCount > 0) {
      gioTrack('MB35555', <String, dynamic>{
        'device_count': familyDeviceCount,
        'device_count_ext': countModel.offlineCount,
        'device_count_ext2': countModel.loadingCount,
        'device_count_ext3': countModel.specialOfflineCount,
        'device_count_ext4': countModel.longTimeCount,
        'app_version': appVersion
      });
    }

    if (_typeCodeMap.isNotEmpty) {
      _typeCodeMap.forEach((String key, OfflineCountModel value) {
        gioTrack('MB35559', <String, dynamic>{
          'product_type': key,
          'device_count': value.totalCount,
          'device_count_ext': value.offlineCount,
          'device_count_ext2': value.loadingCount,
          'device_count_ext3': value.specialOfflineCount,
          'device_count_ext4': value.longTimeCount,
          'app_version': appVersion
        });
      });
    }

    if (_offlineDeviceIds.isNotEmpty &&
        _offlineDeviceIds.length <= OfflineGIOConfig().model.deviceCount) {
      gioTrack('MB35560', <String, dynamic>{
        'extend_info':
            base64.encode(utf8.encode(jsonEncode(_offlineDeviceIds))),
        'timestamp': HTTP.getTimestamp().toString()
      });
    }
  }
}

void _updateOfflineCountModel(
  OfflineStatusModel statusModel,
  OfflineCountModel appTypeCodeModel,
  OfflineCountModel deviceCountModel,
  List<String> offlineDeviceIds,
) {
  appTypeCodeModel.totalCount += 1;
  deviceCountModel.totalCount += 1;

  if (statusModel.isLoading) {
    appTypeCodeModel.loadingCount += 1;
    deviceCountModel.loadingCount += 1;
  } else if (statusModel.isWaitDeployNetwork || statusModel.isSpecialOffline) {
    appTypeCodeModel.specialOfflineCount += 1;
    deviceCountModel.specialOfflineCount += 1;
  } else if (statusModel.isLongTime) {
    appTypeCodeModel.longTimeCount += 1;
    deviceCountModel.longTimeCount += 1;
  } else if (statusModel.onlineState == '0') {
    appTypeCodeModel.offlineCount += 1;
    deviceCountModel.offlineCount += 1;
    offlineDeviceIds.add(statusModel.deviceId);
  }
}

bool _isNeedMarked(SmartHomeDeviceBasicInfo basicInfo) {
  return switch ((
    basicInfo.netType != 'nonNetDevice',
    basicInfo.deviceRole != '3',
    communicationModeType(basicInfo.communicationMode.toLowerCase())
  )) {
    (
      true,
      true,
      CommunicationModeType.wifi ||
          CommunicationModeType.nb ||
          CommunicationModeType.fourthG ||
          CommunicationModeType.zigbee ||
          CommunicationModeType.rs
    ) =>
      true,
    _ => false,
  };
}
