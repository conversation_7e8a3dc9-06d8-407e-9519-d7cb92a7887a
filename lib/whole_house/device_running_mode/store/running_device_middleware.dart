import 'package:device_utils/log/log.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_state.dart';

class RunningDeviceMiddleware implements MiddlewareClass<SmartHomeState> {
  @override
  dynamic call(
      Store<SmartHomeState> store, dynamic action, NextDispatcher next) {
    if (action is UpdateDeviceAttributeAction ||
        action is WashDeviceDataLoadFinish) {
      store.state.deviceState.allCardViewModelMap.values
          .forEach((CardBaseViewModel element) {
        if (element is DeviceCardViewModel && element.runningMode) {
          DevLogger.debug(
            tag: SmartHomeConstant.package,
            msg:
                'RunningDeviceMiddleware: running devices: ${element.deviceId} | ${element.deviceName}',
          );
        }
      });
    }
    next(action);
  }
}
