import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart'
    show FamilyRole, ToastHelper, gioTrack;
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/factory/device_card_factory.dart';
import 'package:smart_home/device/reorderable_grid_view/flutter_spanablegrid.dart';
import 'package:smart_home/device/resize_device_card/resize_base_model.dart';
import 'package:smart_home/device/resize_device_card/resize_overlay.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/edit/store/edit_action.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../common/constant_gio.dart';
import '../edit/util/edit_manager.dart';
import '../store/smart_home_store.dart';
import 'aggregation/aggregation_detail/utils/aggregation_presenter.dart';
import 'device_view_model/card_base_view_model.dart';
import 'reorderable_grid_view/reorderable.dart' as reorder;
import 'reorderable_grid_view/reorderable.dart';

typedef DeviceListReorderCallback = void Function(
    List<String> sortedIdList, String roomId);

/// 暂存聚合后不显示的卡片的排序逻辑
Map<String, int> hiddenIds = <String, int>{};

bool dragging = false;

class DeviceListWidget extends StatelessWidget {
  DeviceListWidget(
      {super.key,
      this.physics,
      required this.cardSortIdList,
      this.dragEnable = false,
      this.filterAll = false,
      this.roomId = '',
      required this.onReorder});

  List<String> cardSortIdList;

  ScrollPhysics? physics;

  bool dragEnable;

  bool filterAll = false;

  String roomId;

  DeviceListReorderCallback onReorder;

  GlobalKey<SliverReorderableGridState> reorderableGrid = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: physics,
      shrinkWrap: true,
      slivers: <Widget>[buildSliver(context)],
    );
  }

  Widget _itemBuilder(CardBaseViewModel model, _SortIdListViewModel viewModel,
      List<Key> lockedKeys, int index, BuildContext context) {
    Widget child = _buildCardStoreConnector(model as ResizeBaseModel);
    if (!dragEnable) {
      child = GestureDetector(
        onLongPress: () {
          if (!smartHomeStore.state.isEditState &&
              !lockedKeys.contains(model.key)) {
            if (isFamilyMemberRole()) {
              ToastHelper.showToast(SmartHomeConstant.cardManageWarning);
              return;
            }
            AggregationPresenter.queryAndUpdateAllAggSort();
            smartHomeStore
                .dispatch(EnterEditStateByCardAction(id: model.sortId()));
          }
        },
        child: child,
      );
    }
    return reorder.ReorderableDelayedDragStartListener(
      key: model.key,
      enabled: !lockedKeys.contains(model.key),
      index: index,
      onDragStart: () {
        dragging = true;
        if (!smartHomeStore.state.isEditState &&
            !lockedKeys.contains(model.key)) {
          AggregationPresenter.queryAndUpdateAllAggSort();
          smartHomeStore
              .dispatch(EnterEditStateByCardAction(id: model.sortId()));
        }
      },
      child: child,
    );
  }

  Widget _buildCardStoreConnector(ResizeBaseModel viewModel) {
    removeOverlay();
    if (viewModel is CardBaseViewModel) {
      return StoreConnector<SmartHomeState, CardIdentificationViewModel>(
        key: UniqueKey(),
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return CardIdentificationViewModel(
              store.state.deviceState.allCardViewModelMap[
                  (viewModel.key as ValueKey<String>).value],
              editMode: store.state.isEditState,
              showFloor: store.state.deviceState.cardShowFloor,
              showRoom: true);
        },
        builder: (BuildContext context, CardIdentificationViewModel viewModel) {
          final CardBaseViewModel? model = viewModel.cardViewModel;
          if (model is DeviceCardViewModel) {
            return ResizeOverlay(
              key: model.key,
              child: DeviceCardFactory.deviceCardWidget(model),
              model: model,
              enable: viewModel.editMode,
              onResizeComplete: (num width, num height,
                  DeviceCardType? initSize, CardSide side) {
                final ResizeBaseModel cardViewModel = model;
                cardViewModel.onResizeComplete(
                    width, height, initSize, context, side);
                final Offset? offset = reorderableGrid.currentState?.updateSize(
                  model.key,
                  GridTileOrigin(cardViewModel.crossAxisCellCount,
                      cardViewModel.mainAxisExtent, model.key),
                );
                return offset ?? Offset.zero;
              },
              onResizeEnd: () {
                gioTrack(GioConst.gioDragResizeFinished, <String, String>{
                  'source': SmartHomeEditManager.getSourceForGio(),
                });
                // TODO(fcs): delete
                smartHomeStore.dispatch(UpdateImageRefreshCountAction());
                smartHomeStore.dispatch(UpdateDeviceCardSizeAction());
              },
            );
          } else if (model is CardBaseViewModel) {
            return DeviceCardFactory.otherCardWidget(model);
          }
          return Container();
        },
      );
    }
    return Container();
  }

  Widget buildSliver(BuildContext context) {
    return StoreConnector<SmartHomeState, _SortIdListViewModel>(
        ignoreChange: (SmartHomeState state) => dragResizing,
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return _SortIdListViewModel(
            cardSortIdList,
            store.state.deviceState.unsortedIdList,
            store.state.deviceState.selectedDeviceCategory,
            store.state.deviceState.allCardViewModelMap.values
                .map((CardBaseViewModel e) => StaggeredScrollViewGridItem(
                    e.crossAxisCellCount, e.mainAxisExtent))
                .toList(),
            smartHomeStore.state.aggregationState.aggDeviceIdList,
            false,
            store.state.familyState.familyRole != FamilyRole.member,
          );
        },
        builder: (BuildContext context, _SortIdListViewModel viewModel) {
          final List<String> sortIdList =
              List<String>.from(viewModel.sortIdList);

          final List<CardBaseViewModel> cardViewModelList =
              <CardBaseViewModel>[];
          for (int i = 0; i < sortIdList.length; i++) {
            final CardBaseViewModel? baseViewModel = smartHomeStore
                .state.deviceState.allCardViewModelMap[sortIdList[i]];
            if (baseViewModel != null && baseViewModel is DeviceCardViewModel) {
              // 聚合后的卡片不展示
              // 已经聚合 && 当前在全屋tab
              final CardBaseViewModel viewModelForHidden = smartHomeStore
                  .state.deviceState.allCardViewModelMap[sortIdList[i]]!;
              if (filterAll &&
                  viewModelForHidden is DeviceCardViewModel &&
                  viewModelForHidden.hidden) {
                hiddenIds[sortIdList[i]] = i;
              } else if (viewModel.selectCategory ==
                  SmartHomeConstant.deviceFilterSelectAll) {
                cardViewModelList.add(smartHomeStore
                    .state.deviceState.allCardViewModelMap[sortIdList[i]]!);
              } else if (_isDeviceMatchingCategory(
                  baseViewModel, viewModel.selectCategory)) {
                cardViewModelList.add(smartHomeStore
                    .state.deviceState.allCardViewModelMap[sortIdList[i]]!);
              }
            }
          }

          final List<CardBaseViewModel> unSortedCardViewModelList =
              <CardBaseViewModel>[];

          if (filterAll &&
              viewModel.selectCategory ==
                  SmartHomeConstant.deviceFilterSelectAll) {
            final List<String> unsortedList =
                List<String>.from(viewModel.unsortedIdList);

            if (unsortedList.isNotEmpty) {
              for (int i = 0; i < unsortedList.length; i++) {
                if (smartHomeStore.state.deviceState
                        .allCardViewModelMap[unsortedList[i]] !=
                    null) {
                  if (viewModel.isShowAddCard ||
                      unsortedList[i] != add_device_card_id) {
                    unSortedCardViewModelList.add(smartHomeStore.state
                        .deviceState.allCardViewModelMap[unsortedList[i]]!);
                  }
                }
              }
            }

            cardViewModelList.addAll(unSortedCardViewModelList);
          }

          final List<Widget> cardWidgetList = <Widget>[];

          final List<Key> lockedKeys = unSortedCardViewModelList
              .map((CardBaseViewModel e) => e.key)
              .toList();

          for (int i = 0; i < cardViewModelList.length; i++) {
            cardWidgetList.add(_itemBuilder(
                cardViewModelList[i], viewModel, lockedKeys, i, context));
          }
          reorderableGrid.currentState?.resetItemSize();
          if (cardViewModelList.isEmpty) {
            return _buildNoDevicePlaceHolderWidget();
          }
          return SliverPadding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
            sliver: reorder.SliverReorderableGrid(
              key: reorderableGrid,
              lockedKeys: lockedKeys,
              gridDelegate: HomeGridDelegate(<GridTileOrigin>[
                ...cardViewModelList.map((CardBaseViewModel e) =>
                    GridTileOrigin(
                        e.crossAxisCellCount, e.mainAxisExtent, e.key))
              ]),
              itemBuilder: (BuildContext context, int index) {
                return cardWidgetList[index];
              },
              itemCount: cardViewModelList.length,
              onReorder: (int oldIndex, int newIndex) {
                if (filterAll) {
                  cardSortIdList.removeWhere(
                      (String element) => hiddenIds.keys.contains(element));
                }
                final String origin = cardSortIdList.removeAt(oldIndex);
                cardSortIdList.insert(newIndex, origin);
                if (filterAll) {
                  for (final String key in hiddenIds.keys) {
                    cardSortIdList.insert(hiddenIds[key]!, key);
                  }
                }
                onReorder(cardSortIdList, roomId);
              },
              onReorderStart: (int p0) {},
              onReorderEnd: (int p0) {
                dragging = false;
              },
              proxyDecorator:
                  (Widget child, int index, Animation<double> animation) {
                return Transform.scale(
                  scale: 1.05,
                  child: Material(
                    color: Colors.transparent,
                    child: StoreProvider<SmartHomeState>(
                      store: smartHomeStore,
                      child: child,
                    ),
                  ),
                );
              },
              shadowBuilder: (Widget child) {
                return Container();
              },
            ),
          );
        });
  }

  Widget _buildNoDevicePlaceHolderWidget() {
    return SliverPadding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
      sliver: SliverToBoxAdapter(
        child: Column(
          children: <Widget>[
            const SizedBox(height: 300),
            Image.asset(
              'assets/icons/auto_mode.webp',
              package: SmartHomeConstant.package,
              color: Colors.green,
              height: 50,
              width: 50,
            ),
            const Text('暂无设备'),
            const SizedBox(height: 300),
          ],
        ),
      ),
    );
  }

  bool _isDeviceMatchingCategory(
      DeviceCardViewModel cardViewModel, String selectCategory) {
    final bool isAggregationPage = roomId.isNotEmpty;
    if (isAggregationPage) {
      return true;
    }
    if (selectCategory.isEmpty) {
      return false;
    }
    final SmartHomeDeviceBasicInfo basicInfo = cardViewModel.device.basicInfo;
    return basicInfo.twoGroupingName == selectCategory ||
        basicInfo.categoryGrouping == selectCategory ||
        cardViewModel.categorySet.contains(selectCategory);
  }
}

class _SortIdListViewModel {
  String selectCategory = '';
  List<String> sortIdList = <String>[];
  List<String> unsortedIdList = <String>[];
  List<StaggeredScrollViewGridItem> sizes = <StaggeredScrollViewGridItem>[];
  List<String> aggregations = <String>[];
  bool editMode = false;
  bool isShowAddCard = false;

  _SortIdListViewModel(
    List<String> list,
    List<String> unsortedList,
    String category,
    List<StaggeredScrollViewGridItem> sizeList,
    List<String> aggregationList,
    this.editMode,
    this.isShowAddCard,
  ) {
    sortIdList.addAll(list);
    unsortedIdList.addAll(unsortedList);
    selectCategory = category;
    aggregations.addAll(aggregationList);
    sizes.addAll(sizeList);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _SortIdListViewModel &&
          runtimeType == other.runtimeType &&
          selectCategory == other.selectCategory &&
          listEquals(sortIdList, other.sortIdList) &&
          listEquals(unsortedIdList, other.unsortedIdList) &&
          listEquals(sizes, other.sizes) &&
          listEquals(aggregations, other.aggregations) &&
          editMode == other.editMode &&
          isShowAddCard == other.isShowAddCard;

  @override
  int get hashCode =>
      selectCategory.hashCode ^
      listHashCode(sortIdList) ^
      listHashCode(unsortedIdList) ^
      listHashCode(sizes) ^
      listHashCode(aggregations) ^
      editMode.hashCode ^
      isShowAddCard.hashCode;
}

class CardIdentificationViewModel {
  bool editMode = false;
  bool? showFloor;
  bool? showRoom;
  CardBaseViewModel? cardViewModel;

  CardIdentificationViewModel(this.cardViewModel,
      {this.editMode = false, this.showFloor, this.showRoom});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CardIdentificationViewModel &&
          runtimeType == other.runtimeType &&
          cardViewModel == other.cardViewModel &&
          editMode == other.editMode &&
          showFloor == other.showFloor &&
          showRoom == other.showRoom;

  @override
  int get hashCode =>
      cardViewModel.hashCode ^
      editMode.hashCode ^
      showFloor.hashCode ^
      showRoom.hashCode;

  @override
  String toString() {
    return 'CardIdentificationViewModel{'
        'cardViewModel: $cardViewModel, '
        'editMode: $editMode, '
        'showFloor: $showFloor, '
        'showRoom: $showRoom'
        '}';
  }
}

class StaggeredScrollViewGridItem {
  int crossAxisCellCount;
  double mainAxisExtent;

  StaggeredScrollViewGridItem(this.crossAxisCellCount, this.mainAxisExtent);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StaggeredScrollViewGridItem &&
          runtimeType == other.runtimeType &&
          crossAxisCellCount == other.crossAxisCellCount &&
          mainAxisExtent == other.mainAxisExtent;

  @override
  int get hashCode => crossAxisCellCount.hashCode ^ mainAxisExtent.hashCode;
}
