import 'package:redux/redux.dart';
import 'package:smart_home/device/aggregation/agg_camera/agg_camera_util.dart';
import 'package:smart_home/device/aggregation/agg_camera/aggregation_camera_vm.dart';
import 'package:smart_home/device/aggregation/agg_store/aggregation_device_reducer_support.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart';
import 'package:smart_home/device/aggregation/aggregation_card/view_model/aggregation_devices_by_room_model.dart';
import 'package:smart_home/device/aggregation/aggregation_detail/view_model/detail_room_list_item.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_basic_info.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';

import '../../../../store/smart_home_state.dart';
import '../aggregation_setting/aggregation_setting_sheet.dart';
import '../aggregation_setting/util/aggregation_setting_constant.dart';
import 'aggregation_action.dart';

final Reducer<SmartHomeState> aggregationCombineReducer =
    combineReducers<SmartHomeState>(<Reducer<SmartHomeState>>[
  TypedReducer<SmartHomeState, UpdateAggregationStateAction>(
          _updateAggregationSettingState)
      .call,
  TypedReducer<SmartHomeState, UpdateAggregationSettingListAction>(
          _updateAggregationSettingList)
      .call,
  TypedReducer<SmartHomeState, UpdateCameraAggListAction>(
          _updateAggCameraDetailSortState)
      .call,
  TypedReducer<SmartHomeState, UpdateAggDeviceVmSortAction>(
          _updateAggDeviceVmSortAction)
      .call,
  TypedReducer<SmartHomeState, UpdateAggDetailListLoadingAction>(
          _updateAggDetailListLoadingAction)
      .call,
  TypedReducer<SmartHomeState, SmallCardDragInAggFinishedAction>(
          _smallCardDragInAggFinishedAction)
      .call,
]);

// 修改设置页面的开关状态（开关点击时弹窗确认，确认后请求接口，接口情况后刷新设备列表）
SmartHomeState _updateAggregationSettingState(
    SmartHomeState state, UpdateAggregationStateAction action) {
  final AggregationSettingBaseViewModel? _switchVm =
      state.aggregationState.switchViewModelMap[action.id];
  if (_switchVm is AggregationSettingBaseViewModel) {
    state.aggregationState.switchViewModelMap[action.id] =
        AggregationSettingBaseViewModel(
            id: action.id,
            isSelected: action.isSelected,
            name: _switchVm.name,
            subTitle: _switchVm.subTitle);
  }
  return state;
}

SmartHomeState _updateAggregationSettingList(
    SmartHomeState state, UpdateAggregationSettingListAction action) {
  state.aggregationState.switchViewModelMap.clear();

  final List<AggregationSettingBaseViewModel> viewModels =
      <AggregationSettingBaseViewModel>[
    _createViewModel(
        id: AggregationSettingConstant.agg_light_id,
        isSelected: action.switchStatusData.lightAggStatus == '1',
        name: AggregationSettingConstant.aggLightingSettingName),
    _createViewModel(
        id: AggregationSettingConstant.agg_curtain_id,
        isSelected: action.switchStatusData.curtainAggStatus == '1',
        name: AggregationSettingConstant.aggCurtainSettingName),
    _createViewModel(
        id: AggregationSettingConstant.env_id,
        isSelected: action.switchStatusData.envAggStatus == '1',
        name: AggregationSettingConstant.aggEnvSettingName),
    _createViewModel(
        id: AggregationSettingConstant.offline_id,
        isSelected: action.switchStatusData.offlineAggStatus == '1',
        name: AggregationSettingConstant.aggOfflineSettingName,
        subTitle: AggregationSettingConstant.aggOfflineSettingSub),
    _createViewModel(
        id: AggregationSettingConstant.non_net_id,
        isSelected: action.switchStatusData.nonnetAggStatus == '1',
        name: AggregationSettingConstant.aggNonnetSettingName,
        subTitle: AggregationSettingConstant.aggNonnetSettingSub),
    _createViewModel(
        id: AggregationSettingConstant.camera_id,
        isSelected: action.switchStatusData.cameraAggStatus == '1',
        name: AggregationSettingConstant.aggCameraSettingName)
  ];

  for (final AggregationSettingBaseViewModel vm in viewModels) {
    state.aggregationState.switchViewModelMap[vm.id] = vm;
  }

  return state;
}

AggregationSettingBaseViewModel _createViewModel(
    {required String id,
    required bool isSelected,
    required String name,
    String subTitle = ''}) {
  return AggregationSettingBaseViewModel(
      id: id, isSelected: isSelected, name: name, subTitle: subTitle);
}

SmartHomeState _updateAggCameraDetailSortState(
    SmartHomeState state, UpdateCameraAggListAction action) {
  final bool showLarge = action.isLarge;

  state.aggregationState.aggregationCameraViewModel.cardType = showLarge
      ? AggregationCamerasVMCardType.large
      : AggregationCamerasVMCardType.small;
  final List<String> target = _filterValidCameraDevices(
      action.devs, state.deviceState.allCardViewModelMap);
  state.aggregationState.aggregationCameraViewModel.cameraList = target;
  commitUpdateAggCameraDevices(state, isFromDeviceReducer: false);
  return state;
}

List<String> _filterValidCameraDevices(
    Set<String> devices, Map<String, CardBaseViewModel> viewModels) {
  final List<String> validDevices = <String>[];
  for (final String deviceId in devices) {
    final CardBaseViewModel? viewModel = viewModels[deviceId];
    if (viewModel is DeviceCardViewModel) {
      validDevices.add(deviceId);
    }
  }

  return validDevices;
}

SmartHomeState _updateAggDeviceVmSortAction(
    SmartHomeState state, UpdateAggDeviceVmSortAction action) {
  sortAggDeviceWithoutHome(action.sortData);
  return state;
}

SmartHomeState _updateAggDetailListLoadingAction(
    SmartHomeState state, UpdateAggDetailListLoadingAction action) {
  state.aggregationState.isLoading = true;
  return state;
}

SmartHomeState _smallCardDragInAggFinishedAction(
    SmartHomeState state, SmallCardDragInAggFinishedAction action) {
  final CardBaseViewModel? viewModel = getCardVm(action.deviceId);
  if (viewModel is AggregationBaseViewModel) {
    viewModel.devicesByRoom.forEach(
        (SmartHomeRoomInfo key, AggregationDevicesByRoomViewModel value) {
      if (key.roomId == action.roomId) {
        value.deviceList = <String>[...action.sortedDeviceIdList];
      }
    });
    viewModel.detailRoomsModel.roomList.forEach((DetailRoomListItem element) {
      if (element.roomId == action.roomId) {
        element.deviceList = <String>[...action.sortedDeviceIdList];
      }
    });
    state.deviceState.allCardViewModelMap[action.deviceId] = viewModel;
  }
  return state;
}
