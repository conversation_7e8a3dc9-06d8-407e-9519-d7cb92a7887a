/*
 * 描述：自定义Text
 * 作者：songFJ
 * 创建时间：2025/1/21
 */

import 'package:flutter/material.dart';

import '../widget_common/card_text_style.dart';

class SmartHomeText extends StatelessWidget {
  const SmartHomeText(
      {super.key,
      required this.text,
      this.maxLines = 1,
      this.overflow = TextOverflow.ellipsis,
      required this.fontSize,
      this.fontWeight = FontWeight.w400,
      required this.color,
      this.textAlign = TextAlign.center,
      this.height});

  final String text;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      style: TextStyle(
          fontFamilyFallback: fontFamilyFallback(),
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          height: height,
          leadingDistribution: TextLeadingDistribution.even),
    );
  }
}

class SmartHomeSpan {
  static TextSpan textSpan(
      {required String text,
      required double fontSize,
      required Color color,
      double? height,
      FontWeight? fontWeight,
      List<InlineSpan>? children}) {
    return TextSpan(
      text: text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        height: height,
        leadingDistribution: TextLeadingDistribution.even,
        fontFamilyFallback: fontFamilyFallback(),
      ),
      children: children,
    );
  }
}
