import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:plugin_device/model/device_attribute_model.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';
import 'package:smart_home/device/device_filter/device_filter_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_attribute.dart';
import 'package:smart_home/device/device_presenter.dart';
import 'package:smart_home/device/device_view_model/card_base_view_model.dart';
import 'package:smart_home/device/fridge_foodnums/models/fridge_foodnum_model.dart';
import 'package:smart_home/device/store/device_state.dart';
import 'package:whole_house_music/music/model/song_info_model.dart';

import '../../time_cost_analysis/analysis_presenter.dart';
import '../aggregation/aggregation_detail/model/supportDeviceModel.dart';
import '../device_view_model/camera_view_model.dart';

class DeviceBaseAction {}

class TraceInfo {
  final int traceId;
  final TraceType traceType;

  TraceInfo(this.traceId, this.traceType);
}

class DeviceInfoMapUpdatedAction extends DeviceBaseAction {
  DeviceInfoMapUpdatedAction({
    required this.originalDeviceInfoMap,
    required this.familyId,
    required this.traceInfo,
    required this.triggerType,
  });

  final Map<String, DeviceInfoModel> originalDeviceInfoMap;
  final String familyId;
  final TraceInfo traceInfo;
  final DeviceListFetchTriggerType triggerType;
}

class UpdateCameraMsgAction extends DeviceBaseAction {
  Map<String, CameraMsgVM> data;
  UpdateCameraMsgAction(this.data);
}

class FetchCameraMsgAction extends DeviceBaseAction {}

class UpdateExcludeDeviceList extends DeviceBaseAction {
  List<String> excludeDeviceList;

  UpdateExcludeDeviceList({required this.excludeDeviceList});
}

class UpdateDeviceAttributeAction extends DeviceBaseAction {
  UpdateDeviceAttributeAction(this.attributeMap);

  Map<String, DeviceAttributeModel> attributeMap;
}

class WashDeviceDataLoadFinish extends DeviceBaseAction {
  Set<String> basicInfo;
  Set<String> userPrefer;

  WashDeviceDataLoadFinish(this.basicInfo, this.userPrefer);
}

/// 更新厨下净水机耗材信息
class UpdatePurifiedConsumableNameAndLevelAttributeAction
    extends DeviceBaseAction {
  UpdatePurifiedConsumableNameAndLevelAttributeAction(
      this.deviceId, this.attributes);

  String deviceId;
  List<SmartHomeDeviceAttribute> attributes;
}

class PurifiedConsumeLoadFinish extends DeviceBaseAction {
  List<String> prodNoList;

  PurifiedConsumeLoadFinish(this.prodNoList);
}

class UpdateSelectedFuncSetKey extends DeviceBaseAction {
  final String deviceId;
  final String selectedFuncSetKey;

  UpdateSelectedFuncSetKey(this.deviceId, this.selectedFuncSetKey);
}

class UpdateWashProgram extends DeviceBaseAction {
  final String deviceId;

  UpdateWashProgram(this.deviceId);
}

class UpdatePopupComponentViewModelAction extends DeviceBaseAction {
  PopupComponentViewModel? popupComponentViewModel;

  UpdatePopupComponentViewModelAction(this.popupComponentViewModel);
}

class UpdateSmallCardListFilteredStateAction extends DeviceBaseAction {
  bool isUnfiltered = false;

  UpdateSmallCardListFilteredStateAction(this.isUnfiltered);
}

class UpdateDeviceFilterAction extends DeviceBaseAction {
  Map<String, Map<String, LinkedHashSet<String>>> filterMap =
      <String, Map<String, LinkedHashSet<String>>>{};

  LinkedHashSet<String> allCategory = LinkedHashSet<String>();
  LinkedHashSet<String> shareCategorySet = LinkedHashSet<String>();

  UpdateDeviceFilterAction(
      this.filterMap, this.allCategory, this.shareCategorySet);
}

class SmallCardDragFinishedAction extends DeviceBaseAction {
  SmallCardDragFinishedAction(this.vmList);

  final List<String> vmList;
}

class LargeCardDragFinishedAction extends DeviceBaseAction {
  LargeCardDragFinishedAction(this.vmList);

  final List<String> vmList;
}

class WholeHouseCardDragFinishedAction extends DeviceBaseAction {
  WholeHouseCardDragFinishedAction(this.vmList);

  final List<String> vmList;
}

class CameraCardDragFinishedAction extends DeviceBaseAction {
  CameraCardDragFinishedAction(this.vmList);

  final List<String> vmList;
}

class UpdateDeviceTabIndexAction extends DeviceBaseAction {
  UpdateDeviceTabIndexAction(this.index);

  final int index;
}

class ClearDeviceDataAction extends DeviceBaseAction {
  ClearDeviceDataAction();
}

class UpdateDeviceFilterToInitialAction extends DeviceBaseAction {
  UpdateDeviceFilterToInitialAction();
}

class DeviceFilterConfirmAction extends DeviceBaseAction {
  DeviceFilterConfirmAction(this.selectedRoom, this.selectedFloor,
      this.selectedDeviceCategory, this.selectSpace, this.selectedRoomId);

  final String selectedRoom;
  final String selectedFloor;
  final String selectedDeviceCategory;
  final String selectSpace;
  final String selectedRoomId;
}

class UpdatePopupContextAction extends DeviceBaseAction {
  UpdatePopupContextAction(this.context);

  final BuildContext? context;
}

class ClearDeviceListAction extends DeviceBaseAction {
  ClearDeviceListAction();
}

class UpdateDeviceStatusAction extends DeviceBaseAction {
  UpdateDeviceStatusAction(this.deviceStatus);

  final DeviceStatus deviceStatus;
}

class DeviceMapUpdateAction extends DeviceBaseAction {
  DeviceMapUpdateAction(this.smartHomeDeviceMap, this.allCardViewModelMap,
      this.smallCardSortIdList, this.floorSet, this.tabFilterMap);

  final Map<String, SmartHomeDevice> smartHomeDeviceMap;
  final Map<String, CardBaseViewModel> allCardViewModelMap;
  final List<String> smallCardSortIdList;
  final Set<String> floorSet;
  final Map<TabFilterModel, List<String>> tabFilterMap;
}

enum CameraActionType {
  play,
  playing,
  retry,
  wakeup,
  changeToolBarStatus,
  stop,
  mute,
  error,
  stopAll,
  enterFullScreen,
  exitFullScreen
}

class CameraAction extends DeviceBaseAction {
  final CameraActionType type;
  final CameraPayload payload;
  final CameraType cameraType;

  CameraAction(this.type, this.payload, this.cameraType);

  @override
  String toString() {
    return 'CameraAction{type: $type, payload: $payload ,cameraType:$cameraType} ';
  }
}

class CameraPayload {
  String deviceId;
  bool status;
  bool showMainCameraStream;

  CameraPayload(
    this.deviceId, {
    this.status = false,
    this.showMainCameraStream = true,
  });
}

class UpdateVoiceBoxDeviceAction extends DeviceBaseAction {
  final SongInfoModel songInfoModel;

  UpdateVoiceBoxDeviceAction(this.songInfoModel);
}

class UpdateImageRefreshCountAction {}

class UpdateScrollableScrollPhysicsAction extends DeviceBaseAction {
  UpdateScrollableScrollPhysicsAction();
}

class UpdateAggregationCardAction extends DeviceBaseAction {
  UpdateAggregationCardAction(this.aggregationOperating, this.aggregationId);

  bool aggregationOperating;
  String aggregationId;
}

class UpdateAggregationRoomIndexAction extends DeviceBaseAction {
  UpdateAggregationRoomIndexAction(this.currentIndex, this.aggregationId);

  int currentIndex;
  String aggregationId;
}

class UpdateAggregationRoomOperateAction extends DeviceBaseAction {
  UpdateAggregationRoomOperateAction(
      this.roomOperating, this.aggregationId, this.operatingRoomId);

  bool roomOperating;
  String aggregationId;
  String operatingRoomId;
}

class AddAggDeviceAction extends DeviceBaseAction {
  AddAggDeviceAction({
    required this.aggregationId,
    required this.allDevices,
    required this.sortedAggList,
  });
  String aggregationId;
  List<String> allDevices;
  AggDeviceData sortedAggList;
}

class RemoveAggDeviceAction extends DeviceBaseAction {
  RemoveAggDeviceAction({
    required this.aggregationId,
    required this.allDevices,
    required this.deleteDevice,
    required this.sortedAggList,
  });
  String aggregationId;
  List<String> allDevices;
  String deleteDevice;
  AggDeviceData sortedAggList;
}

// 更新自定义属性
class UpdateCustomAttributeAction extends DeviceBaseAction {
  UpdateCustomAttributeAction(this.deviceId, this.attribute);

  String deviceId;
  SmartHomeDeviceAttribute attribute;
}

class FridgeFoodNumsChangedAction extends DeviceBaseAction {
  FridgeFoodNumsChangedAction(this.foodNumList);
  List<FoodNumItem> foodNumList;
}
