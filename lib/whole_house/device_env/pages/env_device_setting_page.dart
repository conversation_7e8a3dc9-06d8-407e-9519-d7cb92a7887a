import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:reselect/reselect.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/device_env/env_device_setting_selectors.dart';
import 'package:smart_home/whole_house/device_env/view_models/env_device_setting_view_models.dart';
import 'package:smart_home/whole_house/device_env/whole_house_list_check_item_widget.dart';
import 'package:smart_home/widget_common/card_text_style.dart';

import '../env_device_presenter.dart';

/// 环境设备设置页面
///
/// 展示当前空间下所有可用环境设备并支持选择
/// 设备按温度、湿度、空气质量分类展示
class EnvDeviceSettingPage extends StatefulWidget {
  static const String pageName =
      '${SmartHomeConstant.package}/env_device_setting_page';

  const EnvDeviceSettingPage({
    super.key,
    required this.spaceId,
    this.spaceName = '',
  });

  final String spaceId;
  final String spaceName;

  @override
  EnvDeviceSettingPageState createState() => EnvDeviceSettingPageState();
}

class EnvDeviceSettingPageState extends State<EnvDeviceSettingPage> {
  static const String _pageName = 'envDeviceSettingPage';
  String spaceName = '';
  bool isWholeHouse = false;

  @override
  void initState() {
    super.initState();
    // 注册物理返回拦截监听
    _registerBackButtonHandler();

    // 获取空间名称
    _initSpaceInfo();

    // 查询环境设备数据
    EnvDevicePresenter().getWholeHouseEnvDeviceData();
  }

  @override
  void dispose() {
    // 移除物理返回拦截监听
    InterceptSystemBackUtil.cancelInterceptSystemBack(_pageName);
    super.dispose();
  }

  /// 注册返回按钮处理
  void _registerBackButtonHandler() {
    InterceptSystemBackUtil.interceptSystemBack(
      pageName: _pageName,
      callback: _handleBackNavigation,
    );
  }

  /// 处理返回导航
  void _handleBackNavigation() {
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  /// 初始化空间信息
  void _initSpaceInfo() {
    final Store<SmartHomeState> store = smartHomeStore;
    if (store.state.deviceState.selectedRoomId ==
        store.state.familyState.familyId) {
      spaceName = '全屋';
      isWholeHouse = true;
    } else {
      final String floor = store.state.deviceState.cardShowFloor
          ? store.state.deviceState.selectedFloor
          : '';
      spaceName = '$floor${store.state.deviceState.selectedRoom}';
      isWholeHouse = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StoreProvider<SmartHomeState>(
      store: smartHomeStore,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  /// 构建应用栏
  AppBar _buildAppBar() {
    return AppBar(
      toolbarHeight: 44,
      title: _buildTitle(),
      centerTitle: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      systemOverlayStyle: const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
      leading: _buildBackButton(),
    );
  }

  /// 构建返回按钮
  Widget _buildBackButton() {
    return GestureDetector(
      onTap: _handleBackNavigation,
      child: Center(
        child: Image.asset(
          'assets/icons/navi_back.webp',
          package: SmartHomeConstant.package,
          height: 24,
          width: 24,
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return StoreConnector<SmartHomeState, EnvDeviceSettingListViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        final Selector<SmartHomeState, EnvDeviceSettingListViewModel> selector =
            EnvDeviceSettingSelectors.createSelectEnvDeviceSettingListViewModel(
                widget.spaceId);
        return selector(store.state);
      },
      builder: (BuildContext context, EnvDeviceSettingListViewModel viewModel) {
        if (viewModel.isEmpty) {
          return _buildEmptyState(context);
        }

        return _buildListContent(context, viewModel);
      },
    );
  }

  /// 构建页面标题
  Widget _buildTitle() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Text(
          '${widget.spaceName.isNotEmpty ? widget.spaceName : spaceName}环境信息设置',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 17,
            color: const Color(0xFF111111),
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
        const Text(
          '选择提供环境信息的设备',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w400,
            color: Color(0xFF747474),
          ),
        ),
      ],
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Transform.translate(
        offset: const Offset(0, -44.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Image.asset(
              'assets/images/env_device/none_device.webp',
              package: 'smart_home',
              width: 96,
              height: 96,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无可提供环境空气信息的设备',
              style: TextStyle(
                fontSize: 14,
                color: const Color(0xFF8D8D8D),
                fontWeight: FontWeight.w400,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建列表内容
  Widget _buildListContent(
      BuildContext context, EnvDeviceSettingListViewModel viewModel) {
    final List<_ListItem> flattenedItems = _flattenListItems(viewModel.list);

    return ListView.builder(
      itemCount: flattenedItems.length,
      // padding: const EdgeInsets.only(bottom: 16),
      itemBuilder: (BuildContext context, int index) {
        final _ListItem item = flattenedItems[index];

        if (item is _HeaderItem) {
          return _buildCategoryHeader(item.title, topShrink: index == 0);
        } else if (item is _DeviceItem) {
          return _buildDeviceItem(
              context, item.viewModel, index, flattenedItems);
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// 将分类列表转换为扁平列表项
  List<_ListItem> _flattenListItems(
      List<EnvDeviceSettingCategoryViewModel> categories) {
    final List<_ListItem> items = <_ListItem>[];

    for (final EnvDeviceSettingCategoryViewModel category in categories) {
      items.add(_HeaderItem(category.envName));

      for (final EnvDeviceSettingItemViewModel deviceViewModel
          in category.items) {
        items.add(_DeviceItem(deviceViewModel));
      }
    }

    return items;
  }

  /// 构建分类标题
  Widget _buildCategoryHeader(String title, {bool topShrink = false}) {
    return Padding(
      padding: EdgeInsets.only(left: 16, right: 16, top: topShrink ? 0 : 20),
      child: Container(
        height: 44,
        padding: const EdgeInsets.only(left: 4),
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF333333),
            fontFamilyFallback: fontFamilyFallback(),
          ),
        ),
      ),
    );
  }

  /// 构建设备项
  Widget _buildDeviceItem(
      BuildContext context,
      EnvDeviceSettingItemViewModel viewModel,
      int index,
      List<_ListItem> items) {
    // 判断前一个和后一个项目是否也是设备项
    final bool hasPreviousDevice = index > 0 && items[index - 1] is _DeviceItem;
    final bool hasNextDevice =
        index < items.length - 1 && items[index + 1] is _DeviceItem;

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        children: <Widget>[
          // 设备卡片
          WholeHouseListCheckItemWidget.fromViewModel(
            context: context,
            viewModel: viewModel,
            spaceId: widget.spaceId,
            isWholeHouse: isWholeHouse,
            hideTopRadius: hasPreviousDevice,
            hideBottomRadius: hasNextDevice,
          ),
          // 如果有下一个设备，添加分割线
          if (hasNextDevice) _buildDivider(),
        ],
      ),
    );
  }

  /// 构建分隔线
  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Container(
        height: 0.5,
        color: const Color(0xFFEEEEEE),
      ),
    );
  }
}

/// 列表项基类
abstract class _ListItem {}

/// 类别标题项
class _HeaderItem extends _ListItem {
  final String title;

  _HeaderItem(this.title);
}

/// 设备项
class _DeviceItem extends _ListItem {
  final EnvDeviceSettingItemViewModel viewModel;

  _DeviceItem(this.viewModel);
}
